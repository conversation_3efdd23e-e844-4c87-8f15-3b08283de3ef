#!/usr/bin/env python3
"""
Basic Analysis Example

This script demonstrates how to use the modernized measles transmission
modeling classes for a complete analysis workflow.

This example shows:
1. Loading and validating epidemiological data
2. Fitting transmission models
3. Analyzing SIA effectiveness
4. Generating publication-quality figures
5. Exporting results

Usage:
    python examples/basic_analysis_example.py
"""

import os
import sys

# Add src directory to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.models.transmission import MeaslesTransmissionModel
from src.models.survival_analysis import SurvivalAnalysisModel
from src.data.loaders import EpidemiologicalDataLoader
from src.data.validators import DataValidator
from src.analysis.sia_impact import SIAImpactAnalyzer
from src.visualization.figures import FigureGenerator
from src.utils.config import ConfigManager
from src.utils.helpers import ensure_directory_exists


def main():
    """Run a complete analysis example."""
    print("🔬 MEASLES TRANSMISSION ANALYSIS EXAMPLE")
    print("=" * 50)
    
    # Configuration
    state = "lagos"  # Example state
    data_path = "_data/"
    output_path = "_plots/examples/"
    
    # Ensure output directory exists
    ensure_directory_exists(output_path)
    
    try:
        # Step 1: Load and validate data
        print("\n📊 Step 1: Loading and validating data...")
        
        data_loader = EpidemiologicalDataLoader(data_path)
        all_data = data_loader.load_all_data()
        state_data = data_loader.load_state_data(state)
        
        # Validate data quality
        validator = DataValidator()
        validation_results = validator.validate_all_data(
            surveillance_data=all_data['surveillance'],
            coverage_data=all_data['coverage'],
            birth_data=all_data['births'],
            sia_data=all_data['sia_calendar']
        )
        
        print(f"Data validation status: {validation_results['overall_status']}")
        
        # Step 2: Generate immunity profiles using survival analysis
        print("\n🧬 Step 2: Generating immunity profiles...")
        
        survival_model = SurvivalAnalysisModel(state=state)
        survival_model.load_data(data_path)
        immunity_profile = survival_model.compute_immunity_profile()
        transmission_priors = survival_model.generate_transmission_priors()
        
        print(f"Immunity profiles computed for {immunity_profile['birth_year'].nunique()} cohorts")
        
        # Step 3: Fit transmission model
        print("\n🧮 Step 3: Fitting transmission model...")
        
        config = ConfigManager()
        transmission_model = MeaslesTransmissionModel(
            state=state,
            beta_corr=config.get_model_parameters().beta_correlation,
            tau=config.get_model_parameters().tau
        )
        
        transmission_model.load_data(data_path)
        model_results = transmission_model.fit_transmission_parameters()
        
        print(f"Model fitted with R² = {model_results.get('r_squared', 'N/A'):.3f}")
        
        # Step 4: Analyze SIA impact
        print("\n💉 Step 4: Analyzing SIA effectiveness...")
        
        # This would load actual SIA posterior data
        # For demonstration, we'll create a placeholder
        print("SIA impact analysis would be performed here")
        print("(Requires SIA posterior distributions from model fitting)")
        
        # Step 5: Generate figures
        print("\n📈 Step 5: Generating publication figures...")
        
        figure_generator = FigureGenerator()
        
        # Create input data visualization (Figure 1)
        input_fig = figure_generator.create_input_visualization(
            surveillance_data=state_data['cases'],
            birth_data=state_data['births'],
            coverage_data=state_data['coverage'],
            sia_data=state_data['sia_campaigns'],
            state=state,
            save_path=os.path.join(output_path, f"{state}_inputs.png")
        )
        
        # Create transmission model overview (Figure 2)
        transmission_fig = figure_generator.create_transmission_overview(
            model_results=model_results,
            state=state,
            save_path=os.path.join(output_path, f"{state}_transmission.png")
        )
        
        # Step 6: Generate forecasts
        print("\n🔮 Step 6: Generating forecasts...")
        
        forecast = transmission_model.forecast_cases(horizon_months=24)
        print(f"Forecast generated for {len(forecast)} months")
        
        # Step 7: Export results
        print("\n💾 Step 7: Exporting results...")
        
        # Save model summary
        summary_path = os.path.join(output_path, f"{state}_analysis_summary.txt")
        with open(summary_path, 'w') as f:
            f.write(transmission_model.get_model_summary())
            f.write("\n\n")
            f.write(survival_model.get_summary())
        
        print(f"Analysis summary saved to {summary_path}")
        
        # Print final summary
        print("\n" + "=" * 50)
        print("✅ ANALYSIS COMPLETED SUCCESSFULLY")
        print("=" * 50)
        print(f"State analyzed: {state.title()}")
        print(f"Model R²: {model_results.get('r_squared', 'N/A'):.3f}")
        print(f"SIA effectiveness: {model_results.get('mu_mean', 'N/A'):.3f}")
        print(f"Figures saved to: {output_path}")
        print(f"Summary saved to: {summary_path}")
        
        # Display key findings
        print("\n🔍 KEY FINDINGS:")
        print(f"• Initial susceptibility: {transmission_priors.get('S0_mean', 'N/A'):.3f}")
        print(f"• Mean reporting rate: {model_results.get('reporting_rate', [0.005])[0] if isinstance(model_results.get('reporting_rate'), list) else 0.005:.4f}")
        print(f"• Forecast horizon: 24 months")
        
        print("\n📚 RESEARCH CONTEXT:")
        print("This analysis implements the methodology from:")
        print("'Routine immunization intensification, vaccination campaigns,")
        print("and measles transmission in Southern Nigeria' (2025)")
        
    except Exception as e:
        print(f"\n❌ Analysis failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
