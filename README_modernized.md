# Measles Transmission Model - Modernized

This repository contains a modernized, research-friendly implementation of the measles transmission models described in the paper *"Routine immunization intensification, vaccination campaigns, and measles transmission in Southern Nigeria"* (2025).

## 🎯 Overview

The modernized codebase provides:
- **Research-friendly classes** with clear documentation and paper references
- **Backward compatibility** with existing scripts and workflows
- **Comprehensive data validation** and quality checks
- **Publication-quality visualization** tools
- **Modular architecture** for easy extension and reuse

## 📁 Repository Structure

```
├── src/                          # Modernized class-based implementation
│   ├── models/                   # Core transmission models
│   │   ├── transmission.py       # MeaslesTransmissionModel class
│   │   ├── survival_analysis.py  # SurvivalAnalysisModel class
│   │   └── reporting.py          # ReportingRateEstimator class
│   ├── data/                     # Data loading and processing
│   │   ├── loaders.py            # EpidemiologicalDataLoader class
│   │   ├── processors.py         # DemographicProcessor class
│   │   └── validators.py         # DataValidator class
│   ├── analysis/                 # Analysis pipeline
│   │   ├── fitting.py            # Parameter estimation
│   │   ├── forecasting.py        # Out-of-sample prediction
│   │   └── sia_impact.py         # SIA effectiveness analysis
│   ├── visualization/            # Figure generation
│   │   ├── figures.py            # FigureGenerator class
│   │   ├── diagnostics.py        # Diagnostic plots
│   │   └── exports.py            # Result export utilities
│   └── utils/                    # Utilities and configuration
│       ├── config.py             # ConfigManager class
│       └── helpers.py            # Helper functions
├── examples/                     # Usage examples
│   └── basic_analysis_example.py # Complete analysis workflow
├── docs/                         # Documentation
│   ├── modernization_plan.md     # Modernization strategy
│   └── 1.COLLABORATION.md        # Development guidelines
├── _data/                        # Data files (unchanged)
├── _plots/                       # Output figures (unchanged)
├── pickle_jar/                   # Serialized outputs (unchanged)
└── [Original scripts]            # Legacy scripts (preserved)
```

## 🚀 Quick Start

### Using the Modernized Classes

```python
from src.models.transmission import MeaslesTransmissionModel
from src.data.loaders import EpidemiologicalDataLoader

# Load data
loader = EpidemiologicalDataLoader("_data/")
loader.load_all_data()

# Initialize and fit transmission model
model = MeaslesTransmissionModel(state="lagos")
model.load_data("_data/")
results = model.fit_transmission_parameters()

# Generate forecasts
forecast = model.forecast_cases(horizon_months=24)

# Create publication figures
model.plot_results("_plots/lagos_transmission.png")
```

### Using Legacy Scripts (Backward Compatible)

The original scripts continue to work exactly as before:

```bash
# Generate priors (run first)
python GeneratePriors.py

# Generate state summaries (run second)
python GenerateStateSummary.py

# Create individual figures
python VisualizeInputs.py lagos
python TransmissionModel.py lagos
python OutOfSampleTest.py lagos
python SIAImpactPosteriors.py lagos
python SIAImpactAnalysis.py
```

### Complete Analysis Example

```bash
# Run the complete modernized analysis example
python examples/basic_analysis_example.py
```

## 📊 Key Features

### Research-Friendly Design

- **Clear class names** that match paper terminology
- **Comprehensive docstrings** with mathematical formulations and paper references
- **Example usage** in every class and method
- **Research context** explaining the scientific purpose

### Data Management

- **Automatic data validation** with helpful error messages
- **Flexible data loading** supporting multiple file formats
- **Quality checks** for temporal consistency and completeness
- **Graceful error handling** with researcher-friendly messages

### Model Implementation

- **MeaslesTransmissionModel**: Implements the neighborhood SIR model (Paper Equation 3)
- **SurvivalAnalysisModel**: Generates immunity profiles (Paper Appendix 2)
- **ReportingRateEstimator**: Estimates time-varying reporting rates
- **SIAImpactAnalyzer**: Compares intervention effectiveness

### Visualization

- **Publication-quality figures** matching paper standards
- **Consistent styling** and color schemes
- **Interactive diagnostics** for model validation
- **Multiple export formats** (PNG, PDF, SVG)

## 🔄 Backward Compatibility

The modernized implementation maintains 100% backward compatibility:

- **Original scripts work unchanged** - no modifications needed
- **Same command-line interfaces** - identical usage patterns
- **Same output files** - figures and results in same locations
- **Same serialization** - pickle files for batch processing

## 📈 Analysis Workflow

### 1. Data Loading and Validation
```python
from src.data.loaders import EpidemiologicalDataLoader
from src.data.validators import DataValidator

loader = EpidemiologicalDataLoader()
data = loader.load_all_data()

validator = DataValidator()
validation = validator.validate_all_data(data['surveillance'])
```

### 2. Immunity Profile Generation
```python
from src.models.survival_analysis import SurvivalAnalysisModel

survival_model = SurvivalAnalysisModel(state="lagos")
immunity_profile = survival_model.compute_immunity_profile()
priors = survival_model.generate_transmission_priors()
```

### 3. Transmission Model Fitting
```python
from src.models.transmission import MeaslesTransmissionModel

model = MeaslesTransmissionModel(state="lagos")
model.load_data()
results = model.fit_transmission_parameters()
```

### 4. SIA Impact Analysis
```python
from src.analysis.sia_impact import SIAImpactAnalyzer

analyzer = SIAImpactAnalyzer()
analyzer.load_sia_posteriors("pickle_jar/sia_dists_by_state.pkl")
comparison = analyzer.compare_campaign_effectiveness()
```

### 5. Figure Generation
```python
from src.visualization.figures import FigureGenerator

generator = FigureGenerator()
generator.create_transmission_overview(results, state="lagos")
generator.save_all_figures("_plots/")
```

## ⚙️ Configuration

The modernized system supports flexible configuration:

```python
from src.utils.config import ConfigManager

config = ConfigManager()
config.update_model_parameter("beta_correlation", 3.5)
config.update_analysis_setting("confidence_level", 0.99)
config.save_config("config/custom_config.yaml")
```

## 🧪 Testing and Validation

### Data Validation
```python
from src.data.validators import DataValidator

validator = DataValidator(strict_mode=True)
results = validator.validate_all_data(surveillance_data, coverage_data)
print(results['summary'])
```

### Model Validation
```python
# Out-of-sample testing
forecast = model.forecast_cases(horizon_months=36)

# Cross-validation
validation_results = model.validate_model_fit()
```

## 📚 Documentation

- **Class documentation**: Comprehensive docstrings with mathematical details
- **Paper integration**: References to specific equations and sections
- **Usage examples**: Copy-paste examples for common tasks
- **Research context**: Scientific interpretation of results

## 🔬 Research Applications

This modernized implementation supports:

- **Comparative effectiveness studies** of vaccination interventions
- **Forecasting and scenario analysis** for public health planning
- **Parameter sensitivity analysis** and uncertainty quantification
- **Cross-state and cross-region comparisons**
- **Policy impact assessment** for immunization strategies

## 📖 Citation

If you use this code in your research, please cite:

```
Thakkar, N., Oteri, A.J., McCarthy, K. (2025). Routine immunization 
intensification, vaccination campaigns, and measles transmission in 
Southern Nigeria. medRxiv. https://doi.org/10.1101/2025.02.24.25322796
```

## 🤝 Contributing

See [docs/1.COLLABORATION.md](docs/1.COLLABORATION.md) for development guidelines and contribution instructions.

## 📄 License

This project maintains the same license as the original repository.

---

## 🆕 What's New in the Modernized Version

- ✅ **Research-friendly class architecture** with clear naming and documentation
- ✅ **Comprehensive data validation** with helpful error messages
- ✅ **Modular design** enabling component reuse and extension
- ✅ **Publication-quality visualization** tools
- ✅ **Configuration management** for reproducible analyses
- ✅ **Complete backward compatibility** with existing workflows
- ✅ **Extensive documentation** with paper references and examples
- ✅ **Error handling** designed for researchers with limited coding experience

The modernized version makes the sophisticated epidemiological modeling accessible to researchers while preserving all the analytical power of the original implementation.
