# Measles Transmission Model Repository Modernization Plan

## User Requirement

**Summary**: Modernize the intensification research repository by organizing code into a structured `src/` directory with well-defined classes and modules that are accessible to researchers with limited coding knowledge.

**Files affected**:
- All Python scripts in root directory (GeneratePriors.py, TransmissionModel.py, etc.)
- methods/ directory structure
- New src/ directory structure
- Documentation and examples

## 📚 Research Context Analysis

**Research Domain**: Epidemiological modeling of measles transmission in Southern Nigeria
**Core Methodology**: State-level stochastic SIR (Susceptible-Infected-Recovered) transmission models
**Key Research Question**: Comparing 2019 routine immunization intensification vs. mass vaccination campaigns since 2010

**Mathematical Models Implemented**:
- Neighborhood SIR model with seasonal transmission (Equation 3 from paper)
- Survival analysis for immunity profiles (Appendix 2)
- Bayesian parameter estimation with spatial correlation
- Reporting rate estimation with temporal smoothing

**Data Pipeline**:
1. Surveillance data (case-based reporting 2009-2023)
2. Household survey data (DHS/MICS 2008-2021) 
3. Vaccination campaign records (WHO data)
4. Demographic data (births, population)

**Analysis Workflow**:
1. Prior generation (GeneratePriors.py → SurvivalPrior.py per state)
2. State summary compilation (GenerateStateSummary.py)
3. Figure generation (VisualizeInputs.py, TransmissionModel.py, etc.)
4. SIA impact analysis and comparison

## 🏗️ Proposed src/ Directory Structure

```
src/
├── models/
│   ├── __init__.py
│   ├── transmission.py          # NeighborhoodSIR class (core SIR model)
│   ├── survival_analysis.py     # Immunity profile generation
│   └── reporting.py             # Reporting rate estimation
├── data/
│   ├── __init__.py
│   ├── loaders.py              # Data file reading and validation
│   ├── processors.py           # Birth seasonality, coverage estimation
│   └── validators.py           # Data quality checks
├── analysis/
│   ├── __init__.py
│   ├── fitting.py              # Parameter estimation methods
│   ├── forecasting.py          # Out-of-sample prediction
│   └── sia_impact.py           # SIA effectiveness analysis
├── visualization/
│   ├── __init__.py
│   ├── figures.py              # Publication figure generation
│   ├── diagnostics.py          # Model diagnostic plots
│   └── exports.py              # Result export utilities
└── utils/
    ├── __init__.py
    ├── config.py               # Configuration management
    └── helpers.py              # Common utility functions
```

## 📋 Modernization Implementation Plan

### Phase 1: Core Model Classes
- [ ] **Create MeaslesTransmissionModel class** (src/models/transmission.py)
  - Encapsulate NeighborhoodPosterior functionality
  - Add research-friendly docstrings with paper references
  - Include example usage for Lagos State
  
- [ ] **Create SurvivalAnalysisModel class** (src/models/survival_analysis.py)
  - Extract immunity profile generation logic
  - Document mathematical equations from Appendix 2
  - Provide clear parameter interpretation

- [ ] **Create ReportingRateEstimator class** (src/models/reporting.py)
  - Encapsulate temporal smoothing methodology
  - Add validation against serological surveys
  - Include uncertainty quantification

### Phase 2: Data Management Classes
- [ ] **Create EpidemiologicalDataLoader class** (src/data/loaders.py)
  - Handle surveillance data, survey data, vaccination records
  - Validate data formats and completeness
  - Provide helpful error messages for researchers

- [ ] **Create DemographicProcessor class** (src/data/processors.py)
  - Birth seasonality estimation
  - MCV1 coverage interpolation
  - Population dynamics modeling

- [ ] **Create DataValidator class** (src/data/validators.py)
  - Check data consistency across sources
  - Validate temporal alignment
  - Flag potential data quality issues

### Phase 3: Analysis Pipeline Classes
- [ ] **Create ParameterEstimator class** (src/analysis/fitting.py)
  - Bayesian parameter estimation
  - Spatial correlation modeling
  - Convergence diagnostics

- [ ] **Create ForecastValidator class** (src/analysis/forecasting.py)
  - Out-of-sample testing methodology
  - Forecast accuracy metrics
  - Model validation procedures

- [ ] **Create SIAImpactAnalyzer class** (src/analysis/sia_impact.py)
  - Compare intervention effectiveness
  - Quantify dose efficiency
  - Statistical significance testing

### Phase 4: Visualization and Output Classes
- [ ] **Create FigureGenerator class** (src/visualization/figures.py)
  - Publication-quality figure generation
  - Consistent styling and formatting
  - State-specific customization

- [ ] **Create DiagnosticPlotter class** (src/visualization/diagnostics.py)
  - Model fit diagnostics
  - Parameter convergence plots
  - Residual analysis

- [ ] **Create ResultExporter class** (src/visualization/exports.py)
  - Multiple output formats (PDF, PNG, CSV)
  - Metadata preservation
  - Reproducible exports

## 🔄 Backward Compatibility Strategy

### Legacy Script Wrappers
- [ ] **Maintain existing script interfaces**
  - GeneratePriors.py → calls src.models.survival_analysis
  - TransmissionModel.py → calls src.models.transmission
  - All figure scripts → call src.visualization.figures

- [ ] **Preserve command-line arguments**
  - State name parameter support
  - Silent mode (-s) flag
  - Output directory specifications

- [ ] **Maintain data file locations**
  - Keep _data/ directory structure
  - Preserve pickle_jar/ outputs
  - Maintain _plots/ directory

### Migration Path
- [ ] **Phase 1**: Create src/ classes alongside existing scripts
- [ ] **Phase 2**: Update scripts to use new classes internally
- [ ] **Phase 3**: Add deprecation warnings to old direct usage
- [ ] **Phase 4**: Full migration with legacy wrapper maintenance

## 📖 Documentation Strategy

### Research-Oriented Documentation
- [ ] **Class docstrings with paper integration**
  - Reference specific equations and sections
  - Include mathematical formulations
  - Provide research context and interpretation

- [ ] **Usage examples for each major component**
  - Simple copy-paste examples
  - Real data demonstrations
  - Common use case scenarios

- [ ] **Research workflow documentation**
  - Step-by-step analysis guide
  - Parameter interpretation guide
  - Troubleshooting common issues

### Code Documentation Standards
- [ ] **Comprehensive inline comments**
  - Explain research methodology
  - Document parameter meanings
  - Clarify mathematical implementations

- [ ] **Type hints and validation**
  - Clear input/output specifications
  - Runtime validation with helpful errors
  - Research-friendly error messages

## 🎯 Success Criteria

- [ ] All original functionality preserved with identical results
- [ ] New class structure intuitive for epidemiologists
- [ ] Documentation includes paper references and mathematical context
- [ ] Examples demonstrate common use cases with real data
- [ ] Error handling provides helpful guidance for researchers
- [ ] Code organization follows logical research workflow
- [ ] Backward compatibility maintained for existing scripts
- [ ] Performance equivalent or improved compared to original scripts
- [ ] Dependencies clearly documented and minimal
- [ ] Installation and setup process straightforward

## 🚀 Implementation Timeline

**Week 1**: Core model classes (transmission, survival analysis, reporting)
**Week 2**: Data management classes (loaders, processors, validators)  
**Week 3**: Analysis pipeline classes (fitting, forecasting, SIA impact)
**Week 4**: Visualization classes and backward compatibility
**Week 5**: Documentation, examples, and testing
**Week 6**: Final validation and researcher feedback integration

## 🔧 Technical Considerations

### Dependencies
- Maintain Python 3.8 compatibility
- Preserve existing scientific computing stack (numpy, pandas, scipy, matplotlib)
- Add minimal new dependencies only if essential

### Performance
- Ensure no regression in computational performance
- Optimize data loading and processing where possible
- Maintain memory efficiency for large state datasets

### Testing Strategy
- Unit tests for each new class
- Integration tests for full workflow
- Regression tests against existing outputs
- Performance benchmarks
