# Migration Guide: From Legacy Scripts to Modernized Classes

This guide helps users transition from the original script-based workflow to the modernized class-based architecture while maintaining full backward compatibility.

## 🔄 Migration Overview

### Why Migrate?

**Benefits of the Modernized Approach:**
- ✅ **Research-friendly**: Clear class names and comprehensive documentation
- ✅ **Modular**: Reuse components across different analyses
- ✅ **Robust**: Better error handling and data validation
- ✅ **Extensible**: Easy to add new features and analyses
- ✅ **Interactive**: Works well in Jupyter notebooks and interactive sessions

**Backward Compatibility Guarantee:**
- 🔒 **Original scripts work unchanged** - no modifications needed
- 🔒 **Same outputs** - identical results and file locations
- 🔒 **Same interfaces** - command-line arguments preserved
- 🔒 **Same workflows** - existing batch processing continues to work

## 📋 Migration Mapping

### Script-to-Class Mapping

| Original Script | Modernized Class | Purpose |
|----------------|------------------|---------|
| `GeneratePriors.py` + `SurvivalPrior.py` | `SurvivalAnalysisModel` | Immunity profile generation |
| `TransmissionModel.py` | `MeaslesTransmissionModel` | Core transmission modeling |
| `OutOfSampleTest.py` | `ForecastValidator` | Model validation |
| `SIAImpactPosteriors.py` + `SIAImpactAnalysis.py` | `SIAImpactAnalyzer` | SIA effectiveness analysis |
| `VisualizeInputs.py` | `FigureGenerator.create_input_visualization()` | Input data visualization |
| Data loading logic | `EpidemiologicalDataLoader` | Data management |

### Function-to-Method Mapping

| Original Function | Modernized Method | Class |
|------------------|-------------------|-------|
| `nSIR.fit_the_state_model()` | `fit_transmission_parameters()` | `MeaslesTransmissionModel` |
| `nSIR.create_sia_effects()` | `load_sia_data()` | `EpidemiologicalDataLoader` |
| Manual data loading | `load_state_data()` | `EpidemiologicalDataLoader` |
| Manual figure creation | `create_transmission_overview()` | `FigureGenerator` |

## 🚀 Step-by-Step Migration

### Step 1: Understand Your Current Workflow

**Original Workflow:**
```bash
# Current approach
python GeneratePriors.py
python GenerateStateSummary.py
python TransmissionModel.py lagos
python SIAImpactAnalysis.py
```

**What this does:**
1. Generates immunity priors for all states
2. Fits transmission models for all states
3. Creates transmission figure for Lagos
4. Analyzes SIA impact across states

### Step 2: Equivalent Modernized Workflow

**New Approach:**
```python
from src.models.transmission import MeaslesTransmissionModel
from src.models.survival_analysis import SurvivalAnalysisModel
from src.analysis.sia_impact import SIAImpactAnalyzer
from src.data.loaders import EpidemiologicalDataLoader

# 1. Generate immunity priors (replaces GeneratePriors.py)
survival_model = SurvivalAnalysisModel(state="lagos")
survival_model.load_data()
immunity_profile = survival_model.compute_immunity_profile()
priors = survival_model.generate_transmission_priors()

# 2. Fit transmission model (replaces TransmissionModel.py)
transmission_model = MeaslesTransmissionModel(state="lagos")
transmission_model.load_data()
results = transmission_model.fit_transmission_parameters()

# 3. Create figures (replaces VisualizeInputs.py output)
transmission_model.plot_results("_plots/model_overview.png")

# 4. Analyze SIA impact (replaces SIAImpactAnalysis.py)
sia_analyzer = SIAImpactAnalyzer()
# Load posteriors from previous analysis
sia_analyzer.load_sia_posteriors("pickle_jar/sia_dists_by_state.pkl")
comparison = sia_analyzer.compare_campaign_effectiveness()
```

### Step 3: Gradual Migration Strategy

**Phase 1: Keep Using Original Scripts**
- Continue using original scripts for production work
- Experiment with modernized classes for new analyses
- Compare outputs to ensure consistency

**Phase 2: Hybrid Approach**
- Use original scripts for batch processing
- Use modernized classes for interactive analysis
- Gradually replace individual components

**Phase 3: Full Migration**
- Switch to modernized classes for new projects
- Maintain original scripts for legacy workflows
- Use wrapper scripts for backward compatibility

## 🔧 Common Migration Patterns

### Pattern 1: Single State Analysis

**Original:**
```python
# TransmissionModel.py content
import methods.neighborhood_sir as nSIR

state = "lagos"
# ... data loading code ...
neglp = nSIR.fit_the_state_model(state, state_df, state_sias, ...)
# ... plotting code ...
```

**Modernized:**
```python
from src.models.transmission import MeaslesTransmissionModel

model = MeaslesTransmissionModel(state="lagos")
model.load_data()
results = model.fit_transmission_parameters()
model.plot_results()
```

### Pattern 2: Multi-State Processing

**Original:**
```python
# GenerateStateSummary.py pattern
for state in states:
    subprocess.run(f"python TransmissionModel.py {state} -s", shell=True)
```

**Modernized:**
```python
from src.models.transmission import MeaslesTransmissionModel

results = {}
for state in states:
    model = MeaslesTransmissionModel(state=state)
    model.load_data()
    results[state] = model.fit_transmission_parameters()
    model.plot_results(f"_plots/{state}_transmission.png")
```

### Pattern 3: Data Loading

**Original:**
```python
# Manual data loading
epi = pd.read_csv("_data/southern_states_epi_timeseries.csv")
cal = pd.read_csv("_data/imputed_sia_calendar_by_state.csv")
# ... complex processing ...
```

**Modernized:**
```python
from src.data.loaders import EpidemiologicalDataLoader

loader = EpidemiologicalDataLoader()
data = loader.load_all_data()
state_data = loader.load_state_data("lagos")
```

## 🎯 Migration Benefits by Use Case

### For Researchers

**Before (Original Scripts):**
- Need to understand complex script internals
- Difficult to modify or extend analyses
- Limited error messages when things go wrong
- Hard to reuse components

**After (Modernized Classes):**
- Clear, documented interfaces
- Easy to customize and extend
- Helpful error messages with research context
- Modular components for reuse

### For Reproducible Research

**Before:**
```bash
# Complex workflow with many steps
python GeneratePriors.py
python GenerateStateSummary.py
# Wait 20 minutes...
python TransmissionModel.py lagos
# Manual figure compilation
```

**After:**
```python
# Single, clear workflow
from src.models.transmission import MeaslesTransmissionModel

model = MeaslesTransmissionModel(state="lagos")
model.load_data()
results = model.fit_transmission_parameters()
model.plot_results()
print(model.get_model_summary())
```

### For Interactive Analysis

**Before:**
- Scripts designed for batch processing
- Difficult to inspect intermediate results
- Hard to experiment with parameters

**After:**
```python
# Interactive exploration
model = MeaslesTransmissionModel(state="lagos", beta_corr=3.5)
results = model.fit_transmission_parameters()

# Easy parameter exploration
for beta in [2.0, 3.0, 4.0]:
    model.beta_corr = beta
    new_results = model.fit_transmission_parameters()
    print(f"Beta {beta}: R² = {new_results['r_squared']:.3f}")
```

## 🔍 Validation and Testing

### Ensuring Consistency

**1. Compare Outputs:**
```python
# Run both approaches and compare
original_results = run_original_script("lagos")
modernized_results = MeaslesTransmissionModel("lagos").fit_transmission_parameters()

# Check key metrics match
assert abs(original_results['r_squared'] - modernized_results['r_squared']) < 0.001
```

**2. Visual Comparison:**
```python
# Compare figures
original_fig = load_original_figure("_plots/model_overview.png")
modernized_fig = model.plot_results()
# Visual inspection to ensure consistency
```

**3. Statistical Tests:**
```python
# Compare parameter estimates
from scipy.stats import ttest_rel

original_mu = original_results['mu_estimates']
modernized_mu = modernized_results['mu_estimates']
t_stat, p_value = ttest_rel(original_mu, modernized_mu)
assert p_value > 0.05  # No significant difference
```

## 🛠️ Troubleshooting Migration

### Common Issues

**1. Import Errors**
```python
# Problem
from src.models.transmission import MeaslesTransmissionModel
# ModuleNotFoundError

# Solution
import sys
sys.path.append('.')  # Add current directory to path
```

**2. Different Results**
```python
# Problem: Results don't match exactly

# Solution: Check random seeds and parameter settings
model = MeaslesTransmissionModel(
    state="lagos",
    beta_corr=3.0,  # Match original parameters exactly
    tau=26,
    mu_guess=0.1
)
```

**3. Missing Data**
```python
# Problem: Data loading fails

# Solution: Use data validator
from src.data.validators import DataValidator
validator = DataValidator()
validation = validator.validate_all_data(surveillance_data)
print(validation['summary'])
```

### Performance Considerations

**Memory Usage:**
- Modernized classes may use slightly more memory due to object overhead
- Use `del model` to free memory when processing many states

**Speed:**
- Initial runs may be slower due to data validation
- Subsequent runs should be similar speed
- Use `validate_on_load=False` to skip validation for trusted data

## 📈 Advanced Migration

### Custom Workflows

**Creating Custom Analysis Pipelines:**
```python
class CustomAnalysisPipeline:
    def __init__(self, states):
        self.states = states
        self.results = {}
    
    def run_analysis(self):
        for state in self.states:
            model = MeaslesTransmissionModel(state=state)
            model.load_data()
            self.results[state] = model.fit_transmission_parameters()
    
    def compare_states(self):
        # Custom comparison logic
        pass
```

### Integration with Existing Code

**Wrapper Functions:**
```python
def legacy_compatible_analysis(state, serialize=False):
    """Drop-in replacement for original script functionality."""
    model = MeaslesTransmissionModel(state=state)
    model.load_data()
    results = model.fit_transmission_parameters()
    
    if serialize:
        # Save in original format for compatibility
        save_legacy_format(results, f"pickle_jar/{state}_results.pkl")
    
    return results
```

## ✅ Migration Checklist

- [ ] **Understand current workflow** - Document what scripts you currently use
- [ ] **Test modernized equivalent** - Run parallel analysis to compare results
- [ ] **Validate outputs** - Ensure figures and results match
- [ ] **Update documentation** - Document new workflow for your team
- [ ] **Train users** - Provide examples and guidance for new approach
- [ ] **Maintain backward compatibility** - Keep original scripts available
- [ ] **Monitor performance** - Check that new approach meets speed requirements
- [ ] **Plan rollout** - Gradual migration strategy for production workflows

## 🎓 Learning Resources

1. **Start with examples**: Run `examples/basic_analysis_example.py`
2. **Read class documentation**: Each class has comprehensive docstrings
3. **Compare approaches**: Run both original and modernized for same state
4. **Experiment interactively**: Use Jupyter notebooks for exploration
5. **Ask for help**: Error messages include guidance for common issues

Remember: **You don't have to migrate everything at once!** The modernized classes are designed to work alongside the original scripts, allowing for gradual adoption at your own pace.
