# User Guide: Modernized Measles Transmission Modeling

This guide provides comprehensive instructions for using the modernized measles transmission modeling system, designed to be accessible to researchers with limited coding experience.

## 🎯 Getting Started

### Prerequisites

- Python 3.8 or higher
- Required packages (install via `conda env create -f environment.yml`)
- Data files in the `_data/` directory

### Quick Start Checklist

1. ✅ **Verify data availability**: Ensure `_data/` directory contains required CSV files
2. ✅ **Choose your approach**: Use modernized classes OR legacy scripts
3. ✅ **Run analysis**: Follow the workflow for your chosen approach
4. ✅ **Generate figures**: Create publication-quality visualizations
5. ✅ **Export results**: Save outputs for further analysis

## 📊 Understanding the Data

### Required Data Files

The analysis requires several data files in the `_data/` directory:

| File | Description | Required |
|------|-------------|----------|
| `southern_states_epi_timeseries.csv` | Case surveillance data | ✅ Yes |
| `survey_mcv1_summary_stats.csv` | MCV1 coverage estimates | ✅ Yes |
| `imputed_sia_calendar_by_state.csv` | SIA campaign records | ⚠️ Optional |
| `monthly_births_by_state.csv` | Birth rate data | ⚠️ Optional |
| `states_and_regions.csv` | State/region mapping | ✅ Yes |

### Data Validation

Before running analysis, validate your data:

```python
from src.data.loaders import EpidemiologicalDataLoader
from src.data.validators import DataValidator

# Load and validate data
loader = EpidemiologicalDataLoader("_data/")
data = loader.load_all_data()

validator = DataValidator()
validation = validator.validate_all_data(
    surveillance_data=data['surveillance'],
    coverage_data=data['coverage']
)

print(validation['summary'])
```

**Common Data Issues and Solutions:**

- **Missing states**: Check state name spelling (should be lowercase)
- **Date format errors**: Ensure dates are in YYYY-MM-DD format
- **Negative values**: Check for data entry errors in case counts
- **Large gaps**: Verify temporal continuity in surveillance data

## 🔬 Analysis Workflows

### Option 1: Modernized Class-Based Approach (Recommended)

**Best for**: New users, interactive analysis, custom workflows

```python
# Complete analysis workflow
from src.models.transmission import MeaslesTransmissionModel
from src.data.loaders import EpidemiologicalDataLoader

# 1. Load data
loader = EpidemiologicalDataLoader()
state_data = loader.load_state_data("lagos")

# 2. Fit transmission model
model = MeaslesTransmissionModel(state="lagos")
model.load_data("_data/")
results = model.fit_transmission_parameters()

# 3. Generate forecasts
forecast = model.forecast_cases(horizon_months=24)

# 4. Create figures
model.plot_results("_plots/lagos_analysis.png")

# 5. Print summary
print(model.get_model_summary())
```

### Option 2: Legacy Script Approach

**Best for**: Reproducing published results, batch processing

```bash
# Step 1: Generate priors (required first step)
python GeneratePriors.py

# Step 2: Generate state summaries (required second step)  
python GenerateStateSummary.py

# Step 3: Create individual figures
python VisualizeInputs.py lagos      # Figure 1
python TransmissionModel.py lagos    # Figure 2
python OutOfSampleTest.py lagos      # Figure 3
python SIAImpactPosteriors.py lagos  # Figure 4
python SIAImpactAnalysis.py          # Figure 5
```

## 📈 Understanding the Models

### Transmission Model (Core Analysis)

The transmission model implements a discrete-time SIR framework:

```python
from src.models.transmission import MeaslesTransmissionModel

model = MeaslesTransmissionModel(
    state="lagos",
    beta_corr=3.0,    # Seasonal correlation (Paper Eq. 3)
    tau=26,           # Biweekly time steps per year
    mu_guess=0.1      # Initial SIA effectiveness guess
)
```

**Key Parameters:**
- `beta_corr`: Controls seasonal transmission variation (higher = more seasonal)
- `tau`: Time resolution (26 = biweekly, 52 = weekly)
- `mu_guess`: Starting estimate for SIA effectiveness

**Model Outputs:**
- SIA effectiveness (μ): Fraction of doses immunizing susceptible children
- Reporting rate: Fraction of infections detected by surveillance
- Susceptibility: Population fraction susceptible to measles
- Transmission seasonality: Seasonal variation in transmission rate

### Survival Analysis (Immunity Profiles)

Estimates population immunity from vaccination and infection history:

```python
from src.models.survival_analysis import SurvivalAnalysisModel

survival_model = SurvivalAnalysisModel(
    state="lagos",
    mcv1_efficacy=0.825,  # MCV1 vaccine efficacy
    mcv2_efficacy=0.95,   # MCV2 vaccine efficacy
    sia_efficacy=0.8      # SIA vaccine efficacy
)

immunity_profile = survival_model.compute_immunity_profile()
priors = survival_model.generate_transmission_priors()
```

### SIA Impact Analysis

Compares effectiveness of different vaccination campaigns:

```python
from src.analysis.sia_impact import SIAImpactAnalyzer

analyzer = SIAImpactAnalyzer()
analyzer.load_sia_posteriors("pickle_jar/sia_dists_by_state.pkl")
comparison = analyzer.compare_campaign_effectiveness()

print(f"2019 IRI was {comparison['iri_vs_campaigns_ratio']:.1f}x more effective")
```

## 🎨 Creating Figures

### Publication-Quality Figures

```python
from src.visualization.figures import FigureGenerator

generator = FigureGenerator(style="publication", dpi=300)

# Figure 1: Model inputs
generator.create_input_visualization(
    surveillance_data=state_data['cases'],
    birth_data=state_data['births'],
    coverage_data=state_data['coverage'],
    sia_data=state_data['sia_campaigns'],
    state="lagos",
    save_path="_plots/lagos_inputs.png"
)

# Figure 2: Transmission results
generator.create_transmission_overview(
    model_results=results,
    state="lagos", 
    save_path="_plots/lagos_transmission.png"
)
```

### Customizing Figures

```python
# Custom color scheme
custom_colors = {
    "cases": "#FF6B6B",
    "susceptibility": "#4ECDC4", 
    "reporting": "#45B7D1"
}

generator = FigureGenerator(color_scheme=custom_colors)
```

## ⚙️ Configuration and Customization

### Model Configuration

```python
from src.utils.config import ConfigManager

config = ConfigManager()

# Update model parameters
config.update_model_parameter("beta_correlation", 3.5)
config.update_model_parameter("tau", 24)  # Monthly instead of biweekly

# Update analysis settings
config.update_analysis_setting("confidence_level", 0.99)
config.update_analysis_setting("default_state", "oyo")

# Save configuration
config.save_config("config/my_analysis.yaml")
```

### Custom Analysis Settings

```python
# Load custom configuration
config = ConfigManager("config/my_analysis.yaml")
params = config.get_model_parameters()

# Use in model
model = MeaslesTransmissionModel(
    state="oyo",
    beta_corr=params.beta_correlation,
    tau=params.tau
)
```

## 🔍 Interpreting Results

### Model Fit Quality

```python
results = model.fit_transmission_parameters()

print(f"Model R²: {results['r_squared']:.3f}")
# Good fit: R² > 0.8
# Acceptable fit: R² > 0.6
# Poor fit: R² < 0.6
```

### SIA Effectiveness

```python
sia_effectiveness = results['mu_mean']
print(f"SIA effectiveness: {sia_effectiveness:.3f}")

# Interpretation:
# 0.15 = 15% of doses immunized susceptible children
# Higher values indicate more efficient targeting
```

### Reporting Rate

```python
mean_reporting = np.mean(results['reporting_rate'])
print(f"Mean reporting rate: {mean_reporting:.4f}")

# Interpretation:
# 0.002 = 0.2% of infections are reported
# Low rates are common in high-burden settings
```

## 🚨 Troubleshooting

### Common Issues

**1. Import Errors**
```
ModuleNotFoundError: No module named 'src'
```
**Solution**: Run from repository root directory or add to Python path

**2. Data Loading Errors**
```
FileNotFoundError: Data file not found
```
**Solution**: Verify `_data/` directory exists and contains required files

**3. State Not Found**
```
ValueError: No data found for state 'Lagos'
```
**Solution**: Use lowercase state names (e.g., 'lagos' not 'Lagos')

**4. Model Fitting Fails**
```
RuntimeError: Model fitting failed
```
**Solution**: Check data quality and try different initial parameters

### Getting Help

1. **Check data validation**: Run data validator to identify issues
2. **Review error messages**: Error messages include helpful guidance
3. **Try different states**: Some states may have better data quality
4. **Use default parameters**: Start with default settings before customizing

## 📊 Example Analyses

### Single State Analysis

```python
# Complete analysis for one state
from src.models.transmission import MeaslesTransmissionModel

model = MeaslesTransmissionModel(state="lagos")
model.load_data()
results = model.fit_transmission_parameters()
forecast = model.forecast_cases(horizon_months=36)
model.plot_results()

print(f"Key finding: SIA effectiveness = {results['mu_mean']:.3f}")
```

### Multi-State Comparison

```python
# Compare multiple states
states = ["lagos", "oyo", "kano"]
results = {}

for state in states:
    model = MeaslesTransmissionModel(state=state)
    model.load_data()
    results[state] = model.fit_transmission_parameters()

# Compare SIA effectiveness across states
for state, result in results.items():
    print(f"{state.title()}: {result['mu_mean']:.3f}")
```

### Sensitivity Analysis

```python
# Test different parameter values
beta_values = [2.0, 3.0, 4.0]
results = {}

for beta in beta_values:
    model = MeaslesTransmissionModel(state="lagos", beta_corr=beta)
    model.load_data()
    results[beta] = model.fit_transmission_parameters()

# Compare model fits
for beta, result in results.items():
    print(f"Beta {beta}: R² = {result['r_squared']:.3f}")
```

## 📚 Next Steps

1. **Explore examples**: Run `examples/basic_analysis_example.py`
2. **Read the paper**: Understand the scientific methodology
3. **Customize analysis**: Modify parameters for your research questions
4. **Generate figures**: Create publication-quality visualizations
5. **Export results**: Save outputs for further analysis

For more advanced usage, see the API documentation in each module's docstrings.
