"""
Measles Transmission Model Package

This package provides a modernized, research-friendly interface for analyzing
measles transmission dynamics in Southern Nigeria. It implements the methodology
from "Routine immunization intensification, vaccination campaigns, and measles
transmission in Southern Nigeria" (2025).

The package is organized into modules that follow the research workflow:
- models: Core transmission models and statistical methods
- data: Data loading, processing, and validation
- analysis: Parameter estimation, forecasting, and impact analysis  
- visualization: Figure generation and diagnostic plotting
- utils: Configuration and utility functions

Example usage:
    >>> from src.models.transmission import MeaslesTransmissionModel
    >>> model = MeaslesTransmissionModel(state="lagos")
    >>> model.load_data("_data/")
    >>> results = model.fit_transmission_parameters()
    >>> model.plot_results()
"""

__version__ = "1.0.0"
__author__ = "<PERSON><PERSON>"
__email__ = "<EMAIL>"

# Import main classes for convenient access
from .models.transmission import MeaslesTransmissionModel
from .models.survival_analysis import SurvivalAnalysisModel
from .models.reporting import ReportingRateEstimator
from .data.loaders import EpidemiologicalDataLoader
from .analysis.sia_impact import SIAImpactAnalyzer
from .visualization.figures import FigureGenerator

__all__ = [
    'MeaslesTransmissionModel',
    'SurvivalAnalysisModel', 
    'ReportingRateEstimator',
    'EpidemiologicalDataLoader',
    'SIAImpactAnalyzer',
    'FigureGenerator'
]
