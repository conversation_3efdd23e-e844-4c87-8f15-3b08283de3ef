"""
Epidemiological Data Loader

This module provides classes for loading and validating epidemiological data
for measles transmission modeling, including surveillance data, survey data,
and vaccination records.
"""

import os
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
import warnings
from datetime import datetime


class EpidemiologicalDataLoader:
    """
    Loads and validates epidemiological data for measles transmission analysis.
    
    This class handles loading of multiple data sources required for the
    transmission model, including:
    - Case surveillance data from Nigeria's surveillance system
    - Household survey data (DHS/MICS) for coverage estimates
    - Vaccination campaign records from WHO
    - Demographic data (births, population)
    
    The loader performs data validation and provides helpful error messages
    for researchers when data issues are encountered.
    
    Parameters:
        data_path (str): Path to directory containing data files
        validate_on_load (bool): Whether to validate data immediately on load
        
    Example:
        >>> loader = EpidemiologicalDataLoader("_data/")
        >>> data = loader.load_state_data("lagos")
        >>> print(f"Loaded {len(data['cases'])} case records")
        >>> print(f"Time period: {data['date_range']}")
    
    Data Requirements:
        The data directory should contain:
        - southern_states_epi_timeseries.csv: Case surveillance data
        - survey_mcv1_summary_stats.csv: MCV1 coverage estimates
        - imputed_sia_calendar_by_state.csv: SIA campaign records
        - monthly_births_by_state.csv: Birth rate data
        - states_and_regions.csv: State/region mapping
    """
    
    def __init__(self, 
                 data_path: str = "_data/",
                 validate_on_load: bool = True):
        """
        Initialize the data loader.
        
        Args:
            data_path: Path to data directory
            validate_on_load: Whether to validate data on loading
        """
        self.data_path = data_path
        self.validate_on_load = validate_on_load
        
        # Data containers
        self.surveillance_data = None
        self.coverage_data = None
        self.sia_data = None
        self.birth_data = None
        self.state_mapping = None
        
        # Validation results
        self.validation_results = {}
        
    def load_all_data(self) -> Dict[str, pd.DataFrame]:
        """
        Load all available epidemiological data files.
        
        Returns:
            Dictionary containing all loaded datasets:
            - 'surveillance': Case surveillance data
            - 'coverage': MCV1 coverage estimates
            - 'sia_calendar': SIA campaign records
            - 'births': Monthly birth data
            - 'states': State/region mapping
            
        Raises:
            FileNotFoundError: If required data files are missing
            ValueError: If data validation fails
            
        Example:
            >>> all_data = loader.load_all_data()
            >>> states = all_data['surveillance']['state'].unique()
            >>> print(f"Available states: {list(states)}")
        """
        print(f"Loading epidemiological data from {self.data_path}...")
        
        try:
            # Load surveillance data (required)
            self.surveillance_data = self._load_surveillance_data()
            
            # Load coverage data (required)
            self.coverage_data = self._load_coverage_data()
            
            # Load SIA calendar (optional)
            self.sia_data = self._load_sia_calendar()
            
            # Load birth data (optional)
            self.birth_data = self._load_birth_data()
            
            # Load state mapping (required)
            self.state_mapping = self._load_state_mapping()
            
            # Validate data if requested
            if self.validate_on_load:
                self.validation_results = self._validate_all_data()
            
            print("✓ All epidemiological data loaded successfully")
            
            return {
                'surveillance': self.surveillance_data,
                'coverage': self.coverage_data,
                'sia_calendar': self.sia_data,
                'births': self.birth_data,
                'states': self.state_mapping
            }
            
        except Exception as e:
            raise RuntimeError(f"Failed to load epidemiological data: {str(e)}")
    
    def load_state_data(self, state: str) -> Dict[str, Any]:
        """
        Load data for a specific state.
        
        Args:
            state: State name (case-insensitive)
            
        Returns:
            Dictionary with state-specific data:
            - 'cases': Case surveillance time series
            - 'coverage': MCV1 coverage estimates
            - 'sia_campaigns': SIA campaign records
            - 'births': Birth rate data
            - 'date_range': Tuple of (start_date, end_date)
            - 'summary_stats': Basic data summary
            
        Example:
            >>> state_data = loader.load_state_data("Lagos")
            >>> print(state_data['summary_stats'])
        """
        state = state.lower()
        
        # Load all data if not already loaded
        if self.surveillance_data is None:
            self.load_all_data()
        
        # Extract state-specific data
        state_surveillance = self.surveillance_data[
            self.surveillance_data['state'] == state
        ].copy()
        
        if len(state_surveillance) == 0:
            available_states = self.surveillance_data['state'].unique()
            raise ValueError(
                f"No surveillance data found for state '{state}'\n"
                f"Available states: {sorted(available_states)}\n"
                f"Note: State names should be lowercase"
            )
        
        # Get other state-specific data
        state_coverage = self.coverage_data[
            self.coverage_data['state'] == state
        ] if self.coverage_data is not None else pd.DataFrame()
        
        state_sia = self.sia_data[
            self.sia_data['state'] == state
        ] if self.sia_data is not None else pd.DataFrame()
        
        state_births = self.birth_data[
            self.birth_data['state'] == state
        ] if self.birth_data is not None else pd.DataFrame()
        
        # Compute summary statistics
        date_range = (state_surveillance['date'].min(), state_surveillance['date'].max())
        total_cases = state_surveillance['cases'].sum()
        
        summary_stats = {
            'state': state.title(),
            'total_cases': total_cases,
            'date_range': date_range,
            'time_points': len(state_surveillance),
            'mean_cases_per_period': state_surveillance['cases'].mean(),
            'max_cases_per_period': state_surveillance['cases'].max(),
            'has_coverage_data': len(state_coverage) > 0,
            'has_sia_data': len(state_sia) > 0,
            'has_birth_data': len(state_births) > 0
        }
        
        print(f"✓ Data loaded for {state.title()} State")
        print(f"  Time period: {date_range[0]} to {date_range[1]}")
        print(f"  Total cases: {total_cases:,}")
        print(f"  Time points: {len(state_surveillance)}")
        
        return {
            'cases': state_surveillance,
            'coverage': state_coverage,
            'sia_campaigns': state_sia,
            'births': state_births,
            'date_range': date_range,
            'summary_stats': summary_stats
        }
    
    def _load_surveillance_data(self) -> pd.DataFrame:
        """Load case surveillance data."""
        file_path = os.path.join(self.data_path, "southern_states_epi_timeseries.csv")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(
                f"Surveillance data not found: {file_path}\n"
                f"Expected format: CSV with columns ['state', 'date', 'cases']\n"
                f"This file contains case-based surveillance data from 2009-2023."
            )
        
        try:
            data = pd.read_csv(file_path)
            
            # Validate required columns
            required_cols = ['state', 'date', 'cases']
            missing_cols = [col for col in required_cols if col not in data.columns]
            if missing_cols:
                raise ValueError(f"Missing required columns: {missing_cols}")
            
            # Convert date column
            data['date'] = pd.to_datetime(data['date'])
            
            # Ensure cases are numeric and non-negative
            data['cases'] = pd.to_numeric(data['cases'], errors='coerce')
            data['cases'] = data['cases'].fillna(0).clip(lower=0)
            
            # Convert state names to lowercase for consistency
            data['state'] = data['state'].str.lower()
            
            print(f"✓ Surveillance data loaded: {len(data)} records")
            return data
            
        except Exception as e:
            raise ValueError(f"Error loading surveillance data: {str(e)}")
    
    def _load_coverage_data(self) -> pd.DataFrame:
        """Load MCV1 coverage data."""
        file_path = os.path.join(self.data_path, "survey_mcv1_summary_stats.csv")
        
        if not os.path.exists(file_path):
            warnings.warn(f"Coverage data not found: {file_path}")
            return pd.DataFrame()
        
        try:
            data = pd.read_csv(file_path)
            data['state'] = data['state'].str.lower()
            print(f"✓ Coverage data loaded: {len(data)} records")
            return data
        except Exception as e:
            warnings.warn(f"Error loading coverage data: {str(e)}")
            return pd.DataFrame()
    
    def _load_sia_calendar(self) -> pd.DataFrame:
        """Load SIA campaign calendar."""
        file_path = os.path.join(self.data_path, "imputed_sia_calendar_by_state.csv")
        
        if not os.path.exists(file_path):
            warnings.warn(f"SIA calendar not found: {file_path}")
            return pd.DataFrame()
        
        try:
            data = pd.read_csv(file_path)
            data['state'] = data['state'].str.lower()
            print(f"✓ SIA calendar loaded: {len(data)} records")
            return data
        except Exception as e:
            warnings.warn(f"Error loading SIA calendar: {str(e)}")
            return pd.DataFrame()
    
    def _load_birth_data(self) -> pd.DataFrame:
        """Load monthly birth data."""
        file_path = os.path.join(self.data_path, "monthly_births_by_state.csv")
        
        if not os.path.exists(file_path):
            warnings.warn(f"Birth data not found: {file_path}")
            return pd.DataFrame()
        
        try:
            data = pd.read_csv(file_path)
            data['state'] = data['state'].str.lower()
            print(f"✓ Birth data loaded: {len(data)} records")
            return data
        except Exception as e:
            warnings.warn(f"Error loading birth data: {str(e)}")
            return pd.DataFrame()
    
    def _load_state_mapping(self) -> pd.DataFrame:
        """Load state and region mapping."""
        file_path = os.path.join(self.data_path, "states_and_regions.csv")
        
        if not os.path.exists(file_path):
            raise FileNotFoundError(
                f"State mapping not found: {file_path}\n"
                f"This file is required to identify southern states."
            )
        
        try:
            data = pd.read_csv(file_path, index_col=0)
            data['state'] = data['state'].str.lower()
            print(f"✓ State mapping loaded: {len(data)} states")
            return data
        except Exception as e:
            raise ValueError(f"Error loading state mapping: {str(e)}")
    
    def _validate_all_data(self) -> Dict[str, Any]:
        """Validate all loaded data for consistency and completeness."""
        validation_results = {
            'surveillance': self._validate_surveillance_data(),
            'coverage': self._validate_coverage_data(),
            'overall': self._validate_data_consistency()
        }
        
        # Print validation summary
        print("\n📊 Data Validation Summary:")
        for data_type, results in validation_results.items():
            if isinstance(results, dict) and 'status' in results:
                status = "✓" if results['status'] == 'valid' else "⚠"
                print(f"  {status} {data_type.title()}: {results.get('message', 'OK')}")
        
        return validation_results
    
    def _validate_surveillance_data(self) -> Dict[str, Any]:
        """Validate surveillance data quality."""
        if self.surveillance_data is None:
            return {'status': 'missing', 'message': 'No surveillance data loaded'}
        
        issues = []
        
        # Check for missing dates
        if self.surveillance_data['date'].isnull().any():
            issues.append("Missing dates found")
        
        # Check for negative cases
        if (self.surveillance_data['cases'] < 0).any():
            issues.append("Negative case counts found")
        
        # Check temporal coverage
        date_gaps = self.surveillance_data.groupby('state')['date'].apply(
            lambda x: (x.max() - x.min()).days
        )
        if date_gaps.min() < 365:  # Less than 1 year of data
            issues.append("Some states have less than 1 year of data")
        
        status = 'valid' if len(issues) == 0 else 'warning'
        message = 'Data quality OK' if len(issues) == 0 else f"{len(issues)} issues found"
        
        return {
            'status': status,
            'message': message,
            'issues': issues,
            'n_states': self.surveillance_data['state'].nunique(),
            'date_range': (self.surveillance_data['date'].min(), 
                          self.surveillance_data['date'].max())
        }
    
    def _validate_coverage_data(self) -> Dict[str, Any]:
        """Validate coverage data quality."""
        if self.coverage_data is None or len(self.coverage_data) == 0:
            return {'status': 'missing', 'message': 'No coverage data available'}
        
        # Basic validation
        return {'status': 'valid', 'message': 'Coverage data available'}
    
    def _validate_data_consistency(self) -> Dict[str, Any]:
        """Validate consistency across data sources."""
        issues = []
        
        # Check state name consistency
        if self.surveillance_data is not None and self.state_mapping is not None:
            surv_states = set(self.surveillance_data['state'].unique())
            mapped_states = set(self.state_mapping['state'].unique())
            
            missing_states = surv_states - mapped_states
            if missing_states:
                issues.append(f"States in surveillance but not in mapping: {missing_states}")
        
        status = 'valid' if len(issues) == 0 else 'warning'
        message = 'Data consistency OK' if len(issues) == 0 else f"{len(issues)} consistency issues"
        
        return {
            'status': status,
            'message': message,
            'issues': issues
        }
    
    def get_available_states(self) -> List[str]:
        """Get list of available states in the data."""
        if self.surveillance_data is None:
            self.load_all_data()
        
        return sorted(self.surveillance_data['state'].unique())
    
    def get_data_summary(self) -> str:
        """Generate a summary of loaded data."""
        if self.surveillance_data is None:
            return "No data loaded. Call load_all_data() first."
        
        n_states = self.surveillance_data['state'].nunique()
        total_cases = self.surveillance_data['cases'].sum()
        date_range = (self.surveillance_data['date'].min(), 
                     self.surveillance_data['date'].max())
        
        summary = f"""
Epidemiological Data Summary
{'='*35}

Surveillance Data:
  States: {n_states}
  Total cases: {total_cases:,}
  Date range: {date_range[0].strftime('%Y-%m-%d')} to {date_range[1].strftime('%Y-%m-%d')}
  Records: {len(self.surveillance_data):,}

Additional Data:
  Coverage data: {'✓' if self.coverage_data is not None and len(self.coverage_data) > 0 else '✗'}
  SIA calendar: {'✓' if self.sia_data is not None and len(self.sia_data) > 0 else '✗'}
  Birth data: {'✓' if self.birth_data is not None and len(self.birth_data) > 0 else '✗'}
  State mapping: {'✓' if self.state_mapping is not None else '✗'}
"""
        
        return summary
