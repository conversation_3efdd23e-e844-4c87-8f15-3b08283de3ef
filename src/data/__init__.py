"""
Data Management Module

This module handles loading, processing, and validation of epidemiological data
for measles transmission modeling.

Classes:
    EpidemiologicalDataLoader: Load and validate surveillance and survey data
    DemographicProcessor: Process birth rates, seasonality, and coverage data
    DataValidator: Validate data consistency and quality
"""

from .loaders import EpidemiologicalDataLoader
from .processors import DemographicProcessor
from .validators import DataValidator

__all__ = [
    'EpidemiologicalDataLoader',
    'DemographicProcessor', 
    'DataValidator'
]
