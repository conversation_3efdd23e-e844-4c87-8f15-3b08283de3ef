"""
Data Validation Module

This module provides comprehensive data validation for epidemiological
datasets used in measles transmission modeling, ensuring data quality
and consistency across multiple sources.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
import warnings
from datetime import datetime, timedelta


class DataValidator:
    """
    Comprehensive data validation for epidemiological datasets.
    
    This class performs quality checks and validation across all data sources
    used in measles transmission modeling, providing detailed feedback to
    researchers about data quality issues and potential problems.
    
    Validation Categories:
    - Temporal consistency (date ranges, gaps, overlaps)
    - Data completeness (missing values, coverage)
    - Value ranges (realistic bounds, outliers)
    - Cross-dataset consistency (matching states, time periods)
    - Research-specific validation (epidemiological plausibility)
    
    Example:
        >>> validator = DataValidator()
        >>> results = validator.validate_all_data(surveillance_data, coverage_data)
        >>> if results['overall_status'] == 'valid':
        ...     print("✓ All data validation passed")
        >>> else:
        ...     print("⚠ Data issues found:", results['issues'])
    
    References:
        Paper Section 2.1: Data requirements and quality standards
    """
    
    def __init__(self, 
                 strict_mode: bool = False,
                 min_time_coverage_years: float = 1.0):
        """
        Initialize the data validator.
        
        Args:
            strict_mode: Whether to use strict validation criteria
            min_time_coverage_years: Minimum required time coverage in years
        """
        self.strict_mode = strict_mode
        self.min_time_coverage_years = min_time_coverage_years
        
        # Validation thresholds
        self.thresholds = {
            'max_monthly_cases': 10000,  # Maximum plausible monthly cases per state
            'min_annual_births': 1000,   # Minimum annual births per state
            'max_annual_births': 2000000,  # Maximum annual births per state
            'min_coverage': 0.0,         # Minimum vaccination coverage
            'max_coverage': 1.0,         # Maximum vaccination coverage
            'max_reporting_rate': 1.0,   # Maximum reporting rate
            'min_population': 100000,    # Minimum state population
            'max_case_fatality_rate': 0.1  # Maximum plausible CFR
        }
    
    def validate_all_data(self, 
                         surveillance_data: pd.DataFrame,
                         coverage_data: Optional[pd.DataFrame] = None,
                         birth_data: Optional[pd.DataFrame] = None,
                         sia_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """
        Perform comprehensive validation across all datasets.
        
        Args:
            surveillance_data: Case surveillance data (required)
            coverage_data: Vaccination coverage data (optional)
            birth_data: Birth rate data (optional)
            sia_data: SIA campaign data (optional)
            
        Returns:
            Dictionary with comprehensive validation results:
            - 'overall_status': 'valid', 'warning', or 'invalid'
            - 'surveillance': Surveillance data validation results
            - 'coverage': Coverage data validation results
            - 'births': Birth data validation results
            - 'sia': SIA data validation results
            - 'consistency': Cross-dataset consistency results
            - 'summary': Human-readable summary
            - 'recommendations': Suggested actions for issues
            
        Example:
            >>> results = validator.validate_all_data(surveillance_df, coverage_df)
            >>> print(results['summary'])
        """
        print("🔍 Performing comprehensive data validation...")
        
        validation_results = {
            'surveillance': self.validate_surveillance_data(surveillance_data),
            'coverage': self.validate_coverage_data(coverage_data) if coverage_data is not None else None,
            'births': self.validate_birth_data(birth_data) if birth_data is not None else None,
            'sia': self.validate_sia_data(sia_data) if sia_data is not None else None,
            'consistency': self.validate_cross_dataset_consistency(
                surveillance_data, coverage_data, birth_data, sia_data
            )
        }
        
        # Determine overall status
        overall_status = self._determine_overall_status(validation_results)
        
        # Generate summary and recommendations
        summary = self._generate_validation_summary(validation_results)
        recommendations = self._generate_recommendations(validation_results)
        
        final_results = {
            'overall_status': overall_status,
            'summary': summary,
            'recommendations': recommendations,
            **validation_results
        }
        
        # Print validation summary
        self._print_validation_summary(final_results)
        
        return final_results
    
    def validate_surveillance_data(self, data: pd.DataFrame) -> Dict[str, Any]:
        """
        Validate case surveillance data quality and consistency.
        
        Args:
            data: Surveillance data with cases by state and time
            
        Returns:
            Dictionary with validation results for surveillance data
        """
        if data is None or len(data) == 0:
            return {
                'status': 'invalid',
                'message': 'No surveillance data provided',
                'issues': ['Missing surveillance data'],
                'critical_issues': ['Missing surveillance data']
            }
        
        issues = []
        critical_issues = []
        warnings_list = []
        
        # Check required columns
        required_columns = ['state', 'date', 'cases']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            critical_issues.append(f"Missing required columns: {missing_columns}")
        
        # Validate data types and convert if necessary
        try:
            data['date'] = pd.to_datetime(data['date'])
            data['cases'] = pd.to_numeric(data['cases'], errors='coerce')
        except Exception as e:
            critical_issues.append(f"Data type conversion failed: {str(e)}")
        
        # Check for missing values
        missing_dates = data['date'].isnull().sum()
        missing_cases = data['cases'].isnull().sum()
        missing_states = data['state'].isnull().sum()
        
        if missing_dates > 0:
            issues.append(f"{missing_dates} missing dates")
        if missing_cases > 0:
            issues.append(f"{missing_cases} missing case counts")
        if missing_states > 0:
            critical_issues.append(f"{missing_states} missing state names")
        
        # Check for negative case counts
        if 'cases' in data.columns:
            negative_cases = (data['cases'] < 0).sum()
            if negative_cases > 0:
                issues.append(f"{negative_cases} negative case counts")
        
        # Check for unrealistic case counts
        if 'cases' in data.columns:
            max_cases = data['cases'].max()
            if max_cases > self.thresholds['max_monthly_cases']:
                warnings_list.append(f"Very high case count detected: {max_cases}")
        
        # Check temporal coverage
        if 'date' in data.columns and not data['date'].isnull().all():
            date_range = data['date'].max() - data['date'].min()
            coverage_years = date_range.days / 365.25
            
            if coverage_years < self.min_time_coverage_years:
                issues.append(f"Insufficient temporal coverage: {coverage_years:.1f} years")
        
        # Check for temporal gaps by state
        if 'state' in data.columns and 'date' in data.columns:
            gap_issues = self._check_temporal_gaps(data)
            issues.extend(gap_issues)
        
        # Check state coverage
        if 'state' in data.columns:
            n_states = data['state'].nunique()
            if n_states < 5:  # Expect multiple southern states
                warnings_list.append(f"Only {n_states} states found, expected more")
        
        # Determine status
        if critical_issues:
            status = 'invalid'
        elif issues:
            status = 'warning'
        else:
            status = 'valid'
        
        return {
            'status': status,
            'issues': issues,
            'critical_issues': critical_issues,
            'warnings': warnings_list,
            'n_records': len(data),
            'n_states': data['state'].nunique() if 'state' in data.columns else 0,
            'date_range': (data['date'].min(), data['date'].max()) if 'date' in data.columns else None,
            'total_cases': data['cases'].sum() if 'cases' in data.columns else 0
        }
    
    def validate_coverage_data(self, data: Optional[pd.DataFrame]) -> Dict[str, Any]:
        """Validate vaccination coverage data."""
        if data is None or len(data) == 0:
            return {
                'status': 'warning',
                'message': 'No coverage data provided',
                'issues': ['Missing coverage data - will use defaults']
            }
        
        issues = []
        warnings_list = []
        
        # Check coverage values are in valid range
        if 'coverage' in data.columns:
            invalid_coverage = ((data['coverage'] < self.thresholds['min_coverage']) | 
                               (data['coverage'] > self.thresholds['max_coverage'])).sum()
            if invalid_coverage > 0:
                issues.append(f"{invalid_coverage} coverage values outside [0,1] range")
        
        # Check for reasonable coverage trends
        if 'coverage' in data.columns and 'date' in data.columns:
            coverage_trends = self._check_coverage_trends(data)
            warnings_list.extend(coverage_trends)
        
        status = 'warning' if issues else 'valid'
        
        return {
            'status': status,
            'issues': issues,
            'warnings': warnings_list,
            'n_records': len(data),
            'coverage_range': (data['coverage'].min(), data['coverage'].max()) if 'coverage' in data.columns else None
        }
    
    def validate_birth_data(self, data: Optional[pd.DataFrame]) -> Dict[str, Any]:
        """Validate birth rate data."""
        if data is None or len(data) == 0:
            return {
                'status': 'warning',
                'message': 'No birth data provided',
                'issues': ['Missing birth data - will use estimates']
            }
        
        issues = []
        warnings_list = []
        
        # Check birth counts are reasonable
        if 'births' in data.columns:
            # Check for negative births
            negative_births = (data['births'] < 0).sum()
            if negative_births > 0:
                issues.append(f"{negative_births} negative birth counts")
            
            # Check for unrealistic birth rates
            state_annual_births = data.groupby('state')['births'].sum()
            too_low = (state_annual_births < self.thresholds['min_annual_births']).sum()
            too_high = (state_annual_births > self.thresholds['max_annual_births']).sum()
            
            if too_low > 0:
                warnings_list.append(f"{too_low} states with very low birth rates")
            if too_high > 0:
                warnings_list.append(f"{too_high} states with very high birth rates")
        
        status = 'warning' if issues else 'valid'
        
        return {
            'status': status,
            'issues': issues,
            'warnings': warnings_list,
            'n_records': len(data)
        }
    
    def validate_sia_data(self, data: Optional[pd.DataFrame]) -> Dict[str, Any]:
        """Validate SIA campaign data."""
        if data is None or len(data) == 0:
            return {
                'status': 'warning',
                'message': 'No SIA data provided',
                'issues': ['Missing SIA data - campaigns will not be modeled']
            }
        
        issues = []
        warnings_list = []
        
        # Check for required SIA fields
        expected_fields = ['state', 'date', 'age_group', 'doses']
        missing_fields = [field for field in expected_fields if field not in data.columns]
        if missing_fields:
            issues.append(f"Missing SIA fields: {missing_fields}")
        
        # Check for reasonable dose counts
        if 'doses' in data.columns:
            negative_doses = (data['doses'] < 0).sum()
            if negative_doses > 0:
                issues.append(f"{negative_doses} negative dose counts")
        
        status = 'warning' if issues else 'valid'
        
        return {
            'status': status,
            'issues': issues,
            'warnings': warnings_list,
            'n_campaigns': len(data)
        }
    
    def validate_cross_dataset_consistency(self, 
                                         surveillance_data: pd.DataFrame,
                                         coverage_data: Optional[pd.DataFrame],
                                         birth_data: Optional[pd.DataFrame],
                                         sia_data: Optional[pd.DataFrame]) -> Dict[str, Any]:
        """Validate consistency across datasets."""
        issues = []
        warnings_list = []
        
        # Get state lists from each dataset
        surv_states = set(surveillance_data['state'].unique()) if 'state' in surveillance_data.columns else set()
        
        # Check state name consistency
        if coverage_data is not None and 'state' in coverage_data.columns:
            cov_states = set(coverage_data['state'].unique())
            missing_in_coverage = surv_states - cov_states
            if missing_in_coverage:
                warnings_list.append(f"States in surveillance but not coverage: {missing_in_coverage}")
        
        if birth_data is not None and 'state' in birth_data.columns:
            birth_states = set(birth_data['state'].unique())
            missing_in_births = surv_states - birth_states
            if missing_in_births:
                warnings_list.append(f"States in surveillance but not births: {missing_in_births}")
        
        # Check temporal alignment
        if 'date' in surveillance_data.columns:
            surv_date_range = (surveillance_data['date'].min(), surveillance_data['date'].max())
            
            if coverage_data is not None and 'date' in coverage_data.columns:
                cov_date_range = (coverage_data['date'].min(), coverage_data['date'].max())
                if not self._date_ranges_overlap(surv_date_range, cov_date_range):
                    issues.append("Surveillance and coverage data have no temporal overlap")
        
        status = 'warning' if issues or warnings_list else 'valid'
        
        return {
            'status': status,
            'issues': issues,
            'warnings': warnings_list,
            'n_states_surveillance': len(surv_states),
            'state_consistency': len(surv_states) > 0
        }
    
    def _check_temporal_gaps(self, data: pd.DataFrame) -> List[str]:
        """Check for temporal gaps in surveillance data."""
        issues = []
        
        for state in data['state'].unique():
            state_data = data[data['state'] == state].sort_values('date')
            dates = state_data['date']
            
            # Check for large gaps (>90 days)
            date_diffs = dates.diff()
            large_gaps = (date_diffs > timedelta(days=90)).sum()
            
            if large_gaps > 0:
                issues.append(f"State {state}: {large_gaps} large temporal gaps (>90 days)")
        
        return issues
    
    def _check_coverage_trends(self, data: pd.DataFrame) -> List[str]:
        """Check for unrealistic coverage trends."""
        warnings_list = []
        
        for state in data['state'].unique():
            state_data = data[data['state'] == state].sort_values('date')
            if len(state_data) < 2:
                continue
            
            coverage_values = state_data['coverage'].values
            
            # Check for sudden large changes
            coverage_diffs = np.diff(coverage_values)
            large_changes = (np.abs(coverage_diffs) > 0.3).sum()  # >30% change
            
            if large_changes > 0:
                warnings_list.append(f"State {state}: {large_changes} sudden coverage changes (>30%)")
        
        return warnings_list
    
    def _date_ranges_overlap(self, range1: Tuple, range2: Tuple) -> bool:
        """Check if two date ranges overlap."""
        return range1[0] <= range2[1] and range2[0] <= range1[1]
    
    def _determine_overall_status(self, validation_results: Dict[str, Any]) -> str:
        """Determine overall validation status."""
        statuses = []
        
        for key, result in validation_results.items():
            if result is not None and isinstance(result, dict) and 'status' in result:
                statuses.append(result['status'])
        
        if 'invalid' in statuses:
            return 'invalid'
        elif 'warning' in statuses:
            return 'warning'
        else:
            return 'valid'
    
    def _generate_validation_summary(self, validation_results: Dict[str, Any]) -> str:
        """Generate human-readable validation summary."""
        summary_lines = ["Data Validation Summary", "=" * 25]
        
        for data_type, result in validation_results.items():
            if result is None:
                continue
            
            status_icon = {"valid": "✓", "warning": "⚠", "invalid": "✗"}.get(result.get('status'), "?")
            summary_lines.append(f"{status_icon} {data_type.title()}: {result.get('status', 'unknown')}")
            
            if result.get('issues'):
                for issue in result['issues'][:3]:  # Show first 3 issues
                    summary_lines.append(f"    - {issue}")
        
        return "\n".join(summary_lines)
    
    def _generate_recommendations(self, validation_results: Dict[str, Any]) -> List[str]:
        """Generate recommendations for addressing validation issues."""
        recommendations = []
        
        # Check for critical issues
        for data_type, result in validation_results.items():
            if result is None:
                continue
            
            if result.get('status') == 'invalid':
                recommendations.append(f"Fix critical issues in {data_type} data before proceeding")
            elif result.get('status') == 'warning':
                recommendations.append(f"Review warnings in {data_type} data - may affect results")
        
        # Add general recommendations
        if not recommendations:
            recommendations.append("Data validation passed - proceed with analysis")
        else:
            recommendations.append("Consider data cleaning and re-validation before modeling")
        
        return recommendations
    
    def _print_validation_summary(self, results: Dict[str, Any]) -> None:
        """Print validation summary to console."""
        print("\n" + results['summary'])
        
        if results['recommendations']:
            print("\n📋 Recommendations:")
            for rec in results['recommendations']:
                print(f"  • {rec}")
        
        overall_status = results['overall_status']
        if overall_status == 'valid':
            print("\n✅ Data validation completed successfully")
        elif overall_status == 'warning':
            print("\n⚠️  Data validation completed with warnings")
        else:
            print("\n❌ Data validation failed - critical issues found")
