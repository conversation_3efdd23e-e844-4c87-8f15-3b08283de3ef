"""
Visualization Module

This module provides classes for generating publication-quality figures
and diagnostic plots for measles transmission analysis.

Classes:
    FigureGenerator: Create manuscript figures with consistent styling
    DiagnosticPlotter: Generate model diagnostic and validation plots
    ResultExporter: Export results in multiple formats with metadata
"""

from .figures import FigureGenerator
from .diagnostics import DiagnosticPlotter
from .exports import ResultExporter

__all__ = [
    'FigureGenerator',
    'DiagnosticPlotter',
    'ResultExporter'
]
