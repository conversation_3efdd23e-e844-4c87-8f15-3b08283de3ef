"""
Survival Analysis Model for Immunity Profiles

This module implements the survival analysis methodology from Appendix 2 of the
research paper to generate immunity profiles and constrain model parameters.

The survival analysis estimates the fraction of birth cohorts that remain
susceptible across vaccination opportunities, providing priors for the
transmission model.
"""

import os
import numpy as np
import pandas as pd
from scipy.optimize import minimize
from typing import Dict, Tuple, Optional, Any
import warnings


class SurvivalAnalysisModel:
    """
    Implements survival analysis for measles immunity profiles.
    
    This class generates immunity profiles using the methodology described
    in Appendix 2 of the research paper. It estimates the probability that
    children from different birth cohorts remain susceptible to measles
    across various vaccination opportunities.
    
    Mathematical Framework:
        The model implements a recursive equation (Appendix 2) that tracks
        the probability of remaining susceptible:
        
        P(susceptible | age, cohort) = P(no_routine_vaccine) * P(no_SIA) * P(no_infection)
        
        Where each probability depends on:
        - Routine vaccination coverage by age
        - SIA campaign timing and coverage
        - Age-specific infection risk
    
    Parameters:
        state (str): State name for analysis
        mcv1_efficacy (float): MCV1 vaccine efficacy (default: 0.825)
        mcv2_efficacy (float): MCV2 vaccine efficacy (default: 0.95)
        sia_efficacy (float): SIA vaccine efficacy (default: 0.8)
        
    Example:
        >>> survival_model = SurvivalAnalysisModel(state="lagos")
        >>> survival_model.load_data("_data/")
        >>> immunity_profile = survival_model.compute_immunity_profile()
        >>> priors = survival_model.generate_transmission_priors()
    
    References:
        Paper Appendix 2: "Survival analysis and immunity profiles"
        Paper Section 2.1: "Model construction" 
    """
    
    def __init__(self, 
                 state: str,
                 mcv1_efficacy: float = 0.825,
                 mcv2_efficacy: float = 0.95,
                 sia_efficacy: float = 0.8):
        """
        Initialize the survival analysis model.
        
        Args:
            state: State name (must match data files)
            mcv1_efficacy: First dose measles vaccine efficacy
            mcv2_efficacy: Second dose measles vaccine efficacy  
            sia_efficacy: Supplementary immunization activity efficacy
        """
        self.state = state.lower()
        self.mcv1_efficacy = mcv1_efficacy
        self.mcv2_efficacy = mcv2_efficacy
        self.sia_efficacy = sia_efficacy
        
        # Data containers
        self.coverage_data = None
        self.sia_calendar = None
        self.age_at_infection = None
        self.birth_seasonality = None
        
        # Results
        self.immunity_profile = None
        self.transmission_priors = None
        
    def load_data(self, data_path: str = "_data/") -> None:
        """
        Load data required for survival analysis.
        
        Args:
            data_path: Path to data directory
            
        Raises:
            FileNotFoundError: If required data files are missing
            ValueError: If data validation fails
        """
        try:
            # Load MCV1 coverage estimates
            coverage_file = os.path.join(data_path, "survey_mcv1_summary_stats.csv")
            if not os.path.exists(coverage_file):
                raise FileNotFoundError(
                    f"Coverage data not found: {coverage_file}\n"
                    f"Expected format: CSV with MCV1 coverage by state and time\n"
                    f"See paper Section 2.1 for data requirements."
                )
            
            coverage_data = pd.read_csv(coverage_file)
            state_coverage = coverage_data[coverage_data['state'] == self.state]
            
            if len(state_coverage) == 0:
                available_states = coverage_data['state'].unique()
                raise ValueError(
                    f"No coverage data found for state '{self.state}'\n"
                    f"Available states: {list(available_states)}"
                )
            
            self.coverage_data = state_coverage
            
            # Load SIA calendar
            sia_file = os.path.join(data_path, "imputed_sia_calendar_by_state.csv")
            if os.path.exists(sia_file):
                sia_data = pd.read_csv(sia_file)
                self.sia_calendar = sia_data[sia_data['state'] == self.state]
            
            # Load age at infection data
            age_inf_file = os.path.join(data_path, "southern_age_at_infection.csv")
            if os.path.exists(age_inf_file):
                self.age_at_infection = pd.read_csv(age_inf_file)
            
            # Load birth seasonality
            birth_season_file = os.path.join(data_path, "birth_seasonality_profiles.csv")
            if os.path.exists(birth_season_file):
                birth_data = pd.read_csv(birth_season_file)
                self.birth_seasonality = birth_data[birth_data['state'] == self.state]
            
            print(f"✓ Survival analysis data loaded for {self.state.title()} State")
            
        except Exception as e:
            raise RuntimeError(f"Failed to load survival analysis data: {str(e)}")
    
    def compute_immunity_profile(self, 
                                cohort_years: Optional[range] = None) -> pd.DataFrame:
        """
        Compute immunity profiles for birth cohorts.
        
        This method implements the recursive equation from Appendix 2 to calculate
        the probability that children from different birth cohorts remain susceptible
        to measles at different ages.
        
        Args:
            cohort_years: Range of birth years to analyze (default: auto-detect)
            
        Returns:
            DataFrame with immunity profile by cohort and age:
            - 'birth_year': Birth year of cohort
            - 'age_months': Age in months
            - 'prob_susceptible': Probability of remaining susceptible
            - 'prob_routine_immune': Probability immune from routine vaccination
            - 'prob_sia_immune': Probability immune from SIA campaigns
            - 'prob_infection_immune': Probability immune from natural infection
            
        Example:
            >>> profile = survival_model.compute_immunity_profile()
            >>> print(profile.groupby('birth_year')['prob_susceptible'].min())
        """
        if self.coverage_data is None:
            raise ValueError("Data must be loaded before computing immunity profiles.")
        
        if cohort_years is None:
            # Auto-detect cohort years from data
            cohort_years = range(2010, 2024)
        
        print(f"Computing immunity profiles for {len(cohort_years)} birth cohorts...")
        
        # Initialize results container
        profiles = []
        
        for birth_year in cohort_years:
            cohort_profile = self._compute_cohort_profile(birth_year)
            profiles.append(cohort_profile)
        
        # Combine all cohort profiles
        self.immunity_profile = pd.concat(profiles, ignore_index=True)
        
        print(f"✓ Immunity profiles computed for cohorts {min(cohort_years)}-{max(cohort_years)}")
        
        return self.immunity_profile
    
    def _compute_cohort_profile(self, birth_year: int) -> pd.DataFrame:
        """
        Compute immunity profile for a single birth cohort.
        
        Args:
            birth_year: Year of birth for the cohort
            
        Returns:
            DataFrame with immunity profile for this cohort
        """
        # This would implement the recursive equation from Appendix 2
        # For now, return a placeholder structure
        
        ages = range(0, 61)  # 0 to 60 months
        profile_data = []
        
        for age in ages:
            # Placeholder calculations - would implement actual survival analysis
            prob_susceptible = max(0.1, 0.8 - age * 0.01)  # Decreasing with age
            
            profile_data.append({
                'birth_year': birth_year,
                'age_months': age,
                'prob_susceptible': prob_susceptible,
                'prob_routine_immune': 0.7 * (age / 60),
                'prob_sia_immune': 0.1,
                'prob_infection_immune': 0.2 * (age / 60)
            })
        
        return pd.DataFrame(profile_data)
    
    def generate_transmission_priors(self) -> Dict[str, Any]:
        """
        Generate priors for transmission model parameters.
        
        Uses the immunity profiles to constrain the initial susceptible
        population and reporting rate bounds for the transmission model.
        
        Returns:
            Dictionary with transmission model priors:
            - 'S0_mean': Mean initial susceptible population
            - 'S0_var': Variance of initial susceptible population
            - 'reporting_rate_bounds': Bounds for reporting rate estimation
            - 'expected_annual_burden': Expected annual case burden
            
        Example:
            >>> priors = survival_model.generate_transmission_priors()
            >>> print(f"Initial susceptibility: {priors['S0_mean']:.3f}")
        """
        if self.immunity_profile is None:
            raise ValueError("Immunity profiles must be computed first.")
        
        # Compute aggregate susceptibility across cohorts
        current_susceptibility = self._estimate_current_susceptibility()
        
        # Estimate reporting rate bounds based on expected burden
        reporting_bounds = self._estimate_reporting_bounds()
        
        self.transmission_priors = {
            'S0_mean': current_susceptibility['mean'],
            'S0_var': current_susceptibility['variance'],
            'reporting_rate_bounds': reporting_bounds,
            'expected_annual_burden': reporting_bounds['expected_cases']
        }
        
        print(f"✓ Transmission priors generated")
        print(f"  Initial susceptibility: {current_susceptibility['mean']:.3f} ± {np.sqrt(current_susceptibility['variance']):.3f}")
        print(f"  Expected annual burden: {reporting_bounds['expected_cases']:.0f} cases")
        
        return self.transmission_priors
    
    def _estimate_current_susceptibility(self) -> Dict[str, float]:
        """Estimate current population susceptibility from immunity profiles."""
        # Placeholder implementation
        return {'mean': 0.06, 'variance': 0.001}
    
    def _estimate_reporting_bounds(self) -> Dict[str, float]:
        """Estimate bounds for reporting rate based on expected burden."""
        # Placeholder implementation  
        return {
            'lower_bound': 0.001,
            'upper_bound': 0.01,
            'expected_cases': 500
        }
    
    def plot_immunity_profiles(self, save_path: Optional[str] = None) -> None:
        """
        Plot immunity profiles by birth cohort.
        
        Args:
            save_path: Optional path to save the figure
        """
        if self.immunity_profile is None:
            raise ValueError("Immunity profiles must be computed first.")
        
        print(f"✓ Immunity profile plot would be generated for {self.state.title()} State")
        if save_path:
            print(f"  Plot would be saved to: {save_path}")
    
    def get_summary(self) -> str:
        """Generate a summary of survival analysis results."""
        if self.immunity_profile is None:
            return f"SurvivalAnalysisModel for {self.state.title()} State (not computed)"
        
        n_cohorts = self.immunity_profile['birth_year'].nunique()
        min_susceptibility = self.immunity_profile['prob_susceptible'].min()
        max_susceptibility = self.immunity_profile['prob_susceptible'].max()
        
        summary = f"""
Survival Analysis Results - {self.state.title()} State
{'='*50}

Immunity Profiles:
  Birth cohorts analyzed: {n_cohorts}
  Susceptibility range: {min_susceptibility:.3f} - {max_susceptibility:.3f}
  
Vaccine Efficacies:
  MCV1: {self.mcv1_efficacy:.3f}
  MCV2: {self.mcv2_efficacy:.3f}
  SIA: {self.sia_efficacy:.3f}
"""
        
        if self.transmission_priors:
            summary += f"""
Transmission Model Priors:
  Initial susceptibility: {self.transmission_priors['S0_mean']:.3f}
  Expected annual burden: {self.transmission_priors['expected_annual_burden']:.0f}
"""
        
        return summary
