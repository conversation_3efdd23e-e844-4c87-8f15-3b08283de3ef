"""
Reporting Rate Estimation

This module implements dynamic reporting rate estimation with temporal smoothing
as described in the research paper. The reporting rate represents the fraction
of measles infections that are detected and reported through surveillance.
"""

import numpy as np
import pandas as pd
from scipy.optimize import minimize
from typing import Dict, Tuple, Optional, Any
import warnings


class ReportingRateEstimator:
    """
    Estimates time-varying measles reporting rates with temporal smoothing.
    
    This class implements the methodology described in the paper for estimating
    the fraction of measles infections that are reported as cases. The model
    uses temporal smoothing to regularize reporting rate estimates while
    accounting for surveillance system changes over time.
    
    Mathematical Framework:
        The reporting rate r(t) is estimated by solving:
        
        C(t) ~ Binomial(I(t), r(t))
        
        Where:
        - C(t): Observed cases at time t
        - I(t): True infections at time t (estimated from transmission model)
        - r(t): Reporting rate at time t
        
        Temporal smoothing is applied using a random walk prior:
        r(t) ~ LogitNormal(r(t-1), σ²)
    
    Parameters:
        smoothing_strength (float): Strength of temporal smoothing (default: 3.0)
        min_reporting_rate (float): Minimum allowed reporting rate (default: 0.0005)
        max_reporting_rate (float): Maximum allowed reporting rate (default: 1.0)
        
    Example:
        >>> estimator = ReportingRateEstimator()
        >>> estimator.fit(cases=cases_data, infections=infection_estimates)
        >>> reporting_rates = estimator.get_reporting_rates()
        >>> estimator.plot_reporting_trends()
    
    References:
        Paper Figure 2: Shows estimated reporting rates over time
        Paper Methods: Describes temporal smoothing methodology
    """
    
    def __init__(self, 
                 smoothing_strength: float = 3.0,
                 min_reporting_rate: float = 0.0005,
                 max_reporting_rate: float = 1.0):
        """
        Initialize the reporting rate estimator.
        
        Args:
            smoothing_strength: Controls temporal smoothing (higher = smoother)
            min_reporting_rate: Lower bound for reporting rates
            max_reporting_rate: Upper bound for reporting rates
        """
        self.smoothing_strength = smoothing_strength
        self.min_reporting_rate = min_reporting_rate
        self.max_reporting_rate = max_reporting_rate
        
        # Data containers
        self.cases = None
        self.infections = None
        self.time_points = None
        
        # Results
        self.reporting_rates = None
        self.reporting_rates_var = None
        self.fitted = False
        self.optimization_result = None
        
    def fit(self, 
            cases: np.ndarray,
            infections: np.ndarray,
            prior_rates: Optional[np.ndarray] = None,
            prior_variance: Optional[np.ndarray] = None) -> Dict[str, Any]:
        """
        Fit reporting rates to observed cases and estimated infections.
        
        This method estimates time-varying reporting rates using maximum
        likelihood with temporal smoothing regularization.
        
        Args:
            cases: Array of observed case counts over time
            infections: Array of estimated infection counts over time
            prior_rates: Optional prior reporting rate estimates
            prior_variance: Optional prior variance estimates
            
        Returns:
            Dictionary with fitting results:
            - 'reporting_rates': Estimated reporting rates over time
            - 'reporting_rates_var': Variance estimates
            - 'log_likelihood': Model log-likelihood
            - 'convergence': Optimization convergence info
            
        Raises:
            ValueError: If input arrays have mismatched lengths
            RuntimeError: If optimization fails
            
        Example:
            >>> results = estimator.fit(cases=case_data, infections=infection_data)
            >>> print(f"Mean reporting rate: {results['reporting_rates'].mean():.4f}")
        """
        # Validate inputs
        if len(cases) != len(infections):
            raise ValueError("Cases and infections arrays must have the same length")
        
        if np.any(cases < 0) or np.any(infections < 0):
            raise ValueError("Cases and infections must be non-negative")
        
        self.cases = np.array(cases)
        self.infections = np.array(infections)
        self.time_points = len(cases)
        
        # Set up priors if not provided
        if prior_rates is None:
            # Initialize with simple least squares estimate
            prior_rates = self._initialize_reporting_rates()
        
        if prior_variance is None:
            prior_variance = np.full(self.time_points, 0.01)  # Default variance
        
        try:
            print("Fitting reporting rate model...")
            
            # Set up optimization problem
            initial_params = self._rates_to_logit(prior_rates)
            
            # Create regularization matrix for temporal smoothing
            regularization_matrix = self._create_regularization_matrix()
            
            # Optimize
            result = minimize(
                fun=self._objective_function,
                x0=initial_params,
                args=(regularization_matrix, prior_rates, prior_variance),
                jac=self._objective_gradient,
                method='L-BFGS-B',
                options={'ftol': 1e-13, 'maxcor': 100}
            )
            
            if not result.success:
                warnings.warn(f"Optimization did not converge: {result.message}")
            
            # Extract results
            self.reporting_rates = self._logit_to_rates(result.x)
            self.reporting_rates_var = self._compute_variance_estimates(result)
            self.optimization_result = result
            self.fitted = True
            
            # Compute diagnostics
            log_likelihood = -result.fun
            mean_rate = self.reporting_rates.mean()
            
            print(f"✓ Reporting rate estimation completed")
            print(f"  Mean reporting rate: {mean_rate:.4f}")
            print(f"  Rate range: {self.reporting_rates.min():.4f} - {self.reporting_rates.max():.4f}")
            print(f"  Log-likelihood: {log_likelihood:.2f}")
            
            return {
                'reporting_rates': self.reporting_rates,
                'reporting_rates_var': self.reporting_rates_var,
                'log_likelihood': log_likelihood,
                'convergence': result.success
            }
            
        except Exception as e:
            raise RuntimeError(f"Reporting rate estimation failed: {str(e)}")
    
    def _initialize_reporting_rates(self) -> np.ndarray:
        """Initialize reporting rates using simple least squares."""
        # Avoid division by zero
        safe_infections = np.maximum(self.infections, 1.0)
        initial_rates = np.clip(self.cases / safe_infections, 
                               self.min_reporting_rate, 
                               self.max_reporting_rate)
        return initial_rates
    
    def _create_regularization_matrix(self) -> np.ndarray:
        """Create regularization matrix for temporal smoothing."""
        T = self.time_points
        
        # Second-order difference matrix for random walk smoothing
        D2 = np.diag(T * [-2]) + np.diag((T-1) * [1], k=1) + np.diag((T-1) * [1], k=-1)
        
        # Handle boundary conditions
        D2[0, 2] = 1
        D2[-1, -3] = 1
        
        # Scale by smoothing strength
        regularization = np.dot(D2.T, D2) * ((self.smoothing_strength**4) / 8.0)
        
        return regularization
    
    def _rates_to_logit(self, rates: np.ndarray) -> np.ndarray:
        """Transform reporting rates to logit space for optimization."""
        # Clip rates to avoid numerical issues
        clipped_rates = np.clip(rates, self.min_reporting_rate, 
                               1 - self.min_reporting_rate)
        return np.log(clipped_rates / (1 - clipped_rates))
    
    def _logit_to_rates(self, logit_rates: np.ndarray) -> np.ndarray:
        """Transform logit values back to reporting rates."""
        exp_logit = np.exp(logit_rates)
        rates = exp_logit / (1 + exp_logit)
        return np.clip(rates, self.min_reporting_rate, self.max_reporting_rate)
    
    def _objective_function(self, 
                           logit_rates: np.ndarray,
                           regularization_matrix: np.ndarray,
                           prior_rates: np.ndarray,
                           prior_variance: np.ndarray) -> float:
        """Compute negative log-likelihood with regularization."""
        rates = self._logit_to_rates(logit_rates)
        
        # Data likelihood (binomial)
        # Avoid numerical issues with log(0)
        safe_rates = np.clip(rates, 1e-10, 1 - 1e-10)
        safe_infections = np.maximum(self.infections, 1.0)
        
        # Binomial log-likelihood
        log_likelihood = np.sum(
            self.cases * np.log(safe_rates) + 
            (safe_infections - self.cases) * np.log(1 - safe_rates)
        )
        
        # Regularization penalty
        regularization_penalty = 0.5 * np.dot(logit_rates, 
                                             np.dot(regularization_matrix, logit_rates))
        
        # Prior penalty
        prior_penalty = 0.5 * np.sum((rates - prior_rates)**2 / prior_variance)
        
        # Return negative log-posterior
        return -(log_likelihood - regularization_penalty - prior_penalty)
    
    def _objective_gradient(self, 
                           logit_rates: np.ndarray,
                           regularization_matrix: np.ndarray,
                           prior_rates: np.ndarray,
                           prior_variance: np.ndarray) -> np.ndarray:
        """Compute gradient of objective function."""
        rates = self._logit_to_rates(logit_rates)
        
        # Gradient of logit transformation
        logit_grad = rates * (1 - rates)
        
        # Data likelihood gradient
        safe_infections = np.maximum(self.infections, 1.0)
        likelihood_grad = (self.cases / rates - (safe_infections - self.cases) / (1 - rates))
        
        # Regularization gradient
        reg_grad = np.dot(regularization_matrix, logit_rates)
        
        # Prior gradient
        prior_grad = (rates - prior_rates) / prior_variance
        
        # Chain rule for logit transformation
        total_grad = logit_grad * (likelihood_grad - prior_grad) - reg_grad
        
        return -total_grad  # Negative because we're minimizing
    
    def _compute_variance_estimates(self, optimization_result) -> np.ndarray:
        """Compute variance estimates from Hessian approximation."""
        # This would compute the Hessian and invert it
        # For now, return placeholder values
        return np.full(self.time_points, 0.001)
    
    def get_reporting_rates(self) -> np.ndarray:
        """Get estimated reporting rates."""
        if not self.fitted:
            raise ValueError("Model must be fitted first. Call fit() method.")
        return self.reporting_rates
    
    def get_confidence_intervals(self, confidence_level: float = 0.95) -> Tuple[np.ndarray, np.ndarray]:
        """
        Get confidence intervals for reporting rates.
        
        Args:
            confidence_level: Confidence level (default: 0.95)
            
        Returns:
            Tuple of (lower_bounds, upper_bounds)
        """
        if not self.fitted:
            raise ValueError("Model must be fitted first.")
        
        from scipy.stats import norm
        alpha = 1 - confidence_level
        z_score = norm.ppf(1 - alpha/2)
        
        std_errors = np.sqrt(self.reporting_rates_var)
        lower_bounds = self.reporting_rates - z_score * std_errors
        upper_bounds = self.reporting_rates + z_score * std_errors
        
        # Clip to valid range
        lower_bounds = np.clip(lower_bounds, self.min_reporting_rate, self.max_reporting_rate)
        upper_bounds = np.clip(upper_bounds, self.min_reporting_rate, self.max_reporting_rate)
        
        return lower_bounds, upper_bounds
    
    def plot_reporting_trends(self, save_path: Optional[str] = None) -> None:
        """
        Plot reporting rate trends over time.
        
        Args:
            save_path: Optional path to save the figure
        """
        if not self.fitted:
            raise ValueError("Model must be fitted first.")
        
        print("✓ Reporting rate trend plot would be generated")
        if save_path:
            print(f"  Plot would be saved to: {save_path}")
    
    def get_summary(self) -> str:
        """Generate a summary of reporting rate estimation results."""
        if not self.fitted:
            return "ReportingRateEstimator (not fitted)"
        
        mean_rate = self.reporting_rates.mean()
        min_rate = self.reporting_rates.min()
        max_rate = self.reporting_rates.max()
        
        summary = f"""
Reporting Rate Estimation Results
{'='*40}

Reporting Rates:
  Mean: {mean_rate:.4f} ({mean_rate*100:.2f}%)
  Range: {min_rate:.4f} - {max_rate:.4f}
  
Model Configuration:
  Smoothing strength: {self.smoothing_strength}
  Rate bounds: [{self.min_reporting_rate:.4f}, {self.max_reporting_rate:.4f}]
  Time points: {self.time_points}
  
Optimization:
  Converged: {self.optimization_result.success}
  Function evaluations: {self.optimization_result.nfev}
"""
        return summary
