"""
Helper Utilities

This module contains common utility functions used across the measles
transmission modeling package.
"""

import os
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Optional, Tuple, Union
import warnings
from datetime import datetime, timedelta
import pickle


def ensure_directory_exists(directory_path: str) -> None:
    """
    Ensure a directory exists, creating it if necessary.
    
    Args:
        directory_path: Path to directory
    """
    if not os.path.exists(directory_path):
        os.makedirs(directory_path, exist_ok=True)


def save_results_safely(data: Any, 
                        filepath: str, 
                        backup: bool = True) -> None:
    """
    Safely save results with optional backup.
    
    Args:
        data: Data to save
        filepath: Path to save file
        backup: Whether to create backup if file exists
    """
    # Create backup if file exists
    if backup and os.path.exists(filepath):
        backup_path = f"{filepath}.backup"
        if os.path.exists(backup_path):
            os.remove(backup_path)
        os.rename(filepath, backup_path)
    
    # Ensure directory exists
    ensure_directory_exists(os.path.dirname(filepath))
    
    # Save based on file extension
    if filepath.endswith('.pkl') or filepath.endswith('.pickle'):
        with open(filepath, 'wb') as f:
            pickle.dump(data, f)
    elif filepath.endswith('.csv'):
        if isinstance(data, pd.DataFrame):
            data.to_csv(filepath, index=False)
        else:
            raise ValueError("CSV format requires pandas DataFrame")
    else:
        raise ValueError(f"Unsupported file format: {filepath}")


def load_results_safely(filepath: str) -> Any:
    """
    Safely load results with error handling.
    
    Args:
        filepath: Path to file
        
    Returns:
        Loaded data
        
    Raises:
        FileNotFoundError: If file doesn't exist
        ValueError: If file format is unsupported
    """
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"File not found: {filepath}")
    
    try:
        if filepath.endswith('.pkl') or filepath.endswith('.pickle'):
            with open(filepath, 'rb') as f:
                return pickle.load(f)
        elif filepath.endswith('.csv'):
            return pd.read_csv(filepath)
        else:
            raise ValueError(f"Unsupported file format: {filepath}")
    except Exception as e:
        raise RuntimeError(f"Failed to load {filepath}: {str(e)}")


def validate_state_name(state: str, 
                       available_states: Optional[List[str]] = None) -> str:
    """
    Validate and normalize state name.
    
    Args:
        state: State name to validate
        available_states: Optional list of valid state names
        
    Returns:
        Normalized state name (lowercase)
        
    Raises:
        ValueError: If state name is invalid
    """
    if not isinstance(state, str) or len(state.strip()) == 0:
        raise ValueError("State name must be a non-empty string")
    
    normalized_state = state.lower().strip()
    
    if available_states:
        available_lower = [s.lower() for s in available_states]
        if normalized_state not in available_lower:
            raise ValueError(
                f"Invalid state '{state}'. Available states: {available_states}"
            )
    
    return normalized_state


def compute_date_range_overlap(range1: Tuple[datetime, datetime], 
                              range2: Tuple[datetime, datetime]) -> Optional[Tuple[datetime, datetime]]:
    """
    Compute overlap between two date ranges.
    
    Args:
        range1: First date range (start, end)
        range2: Second date range (start, end)
        
    Returns:
        Overlapping date range or None if no overlap
    """
    start_overlap = max(range1[0], range2[0])
    end_overlap = min(range1[1], range2[1])
    
    if start_overlap <= end_overlap:
        return (start_overlap, end_overlap)
    else:
        return None


def create_time_series_index(start_date: Union[str, datetime], 
                            end_date: Union[str, datetime],
                            frequency: str = 'M') -> pd.DatetimeIndex:
    """
    Create a pandas DatetimeIndex for time series analysis.
    
    Args:
        start_date: Start date
        end_date: End date
        frequency: Frequency string ('M' for monthly, 'W' for weekly, etc.)
        
    Returns:
        DatetimeIndex
    """
    if isinstance(start_date, str):
        start_date = pd.to_datetime(start_date)
    if isinstance(end_date, str):
        end_date = pd.to_datetime(end_date)
    
    return pd.date_range(start=start_date, end=end_date, freq=frequency)


def compute_summary_statistics(data: np.ndarray, 
                              confidence_level: float = 0.95) -> Dict[str, float]:
    """
    Compute comprehensive summary statistics for an array.
    
    Args:
        data: Input data array
        confidence_level: Confidence level for intervals
        
    Returns:
        Dictionary with summary statistics
    """
    if len(data) == 0:
        return {'count': 0}
    
    # Remove NaN values
    clean_data = data[~np.isnan(data)]
    
    if len(clean_data) == 0:
        return {'count': 0, 'all_nan': True}
    
    # Compute percentiles for confidence interval
    alpha = 1 - confidence_level
    lower_percentile = 100 * alpha / 2
    upper_percentile = 100 * (1 - alpha / 2)
    
    return {
        'count': len(clean_data),
        'mean': np.mean(clean_data),
        'std': np.std(clean_data),
        'min': np.min(clean_data),
        'max': np.max(clean_data),
        'median': np.median(clean_data),
        'q25': np.percentile(clean_data, 25),
        'q75': np.percentile(clean_data, 75),
        'ci_lower': np.percentile(clean_data, lower_percentile),
        'ci_upper': np.percentile(clean_data, upper_percentile),
        'skewness': float(pd.Series(clean_data).skew()),
        'kurtosis': float(pd.Series(clean_data).kurtosis())
    }


def format_number_with_uncertainty(mean: float, 
                                  std: float, 
                                  decimals: int = 3) -> str:
    """
    Format a number with uncertainty in scientific notation.
    
    Args:
        mean: Mean value
        std: Standard deviation
        decimals: Number of decimal places
        
    Returns:
        Formatted string like "0.123 ± 0.045"
    """
    return f"{mean:.{decimals}f} ± {std:.{decimals}f}"


def check_data_completeness(data: pd.DataFrame, 
                           required_columns: List[str]) -> Dict[str, Any]:
    """
    Check data completeness and quality.
    
    Args:
        data: DataFrame to check
        required_columns: List of required column names
        
    Returns:
        Dictionary with completeness assessment
    """
    results = {
        'total_rows': len(data),
        'total_columns': len(data.columns),
        'missing_columns': [],
        'column_completeness': {},
        'overall_completeness': 0.0
    }
    
    # Check for missing columns
    missing_columns = [col for col in required_columns if col not in data.columns]
    results['missing_columns'] = missing_columns
    
    # Check completeness for each column
    for col in data.columns:
        if col in required_columns:
            non_null_count = data[col].notna().sum()
            completeness = non_null_count / len(data) if len(data) > 0 else 0
            results['column_completeness'][col] = {
                'completeness': completeness,
                'missing_count': len(data) - non_null_count,
                'non_null_count': non_null_count
            }
    
    # Compute overall completeness
    if required_columns:
        completeness_values = [
            results['column_completeness'].get(col, {}).get('completeness', 0)
            for col in required_columns if col in data.columns
        ]
        results['overall_completeness'] = np.mean(completeness_values) if completeness_values else 0
    
    return results


def create_progress_tracker(total_items: int, 
                           description: str = "Processing") -> callable:
    """
    Create a simple progress tracker function.
    
    Args:
        total_items: Total number of items to process
        description: Description of the process
        
    Returns:
        Function to call for progress updates
    """
    def update_progress(current_item: int, item_name: str = "") -> None:
        """Update progress display."""
        percentage = (current_item / total_items) * 100
        bar_length = 30
        filled_length = int(bar_length * current_item // total_items)
        bar = '█' * filled_length + '-' * (bar_length - filled_length)
        
        print(f'\r{description}: |{bar}| {percentage:.1f}% ({current_item}/{total_items}) {item_name}', 
              end='', flush=True)
        
        if current_item == total_items:
            print()  # New line when complete
    
    return update_progress


def convert_to_research_friendly_names(technical_names: Dict[str, str]) -> Dict[str, str]:
    """
    Convert technical variable names to research-friendly names.
    
    Args:
        technical_names: Dictionary mapping technical to friendly names
        
    Returns:
        Dictionary with research-friendly names
    """
    default_mappings = {
        'mu': 'SIA Effectiveness',
        'beta_corr': 'Seasonal Correlation',
        'tau': 'Time Periods per Year',
        'r_hat': 'Reporting Rate',
        'S0': 'Initial Susceptible Population',
        'mcv1_efficacy': 'MCV1 Vaccine Efficacy',
        'mcv2_efficacy': 'MCV2 Vaccine Efficacy',
        'sia_efficacy': 'SIA Vaccine Efficacy'
    }
    
    # Merge with provided mappings
    friendly_names = {**default_mappings, **technical_names}
    
    return friendly_names


def generate_analysis_timestamp() -> str:
    """
    Generate a timestamp string for analysis outputs.
    
    Returns:
        Timestamp string in format YYYY-MM-DD_HH-MM-SS
    """
    return datetime.now().strftime("%Y-%m-%d_%H-%M-%S")


def validate_numeric_range(value: Union[int, float], 
                          min_val: Optional[float] = None,
                          max_val: Optional[float] = None,
                          parameter_name: str = "parameter") -> None:
    """
    Validate that a numeric value is within specified range.
    
    Args:
        value: Value to validate
        min_val: Minimum allowed value
        max_val: Maximum allowed value
        parameter_name: Name of parameter for error messages
        
    Raises:
        ValueError: If value is outside valid range
    """
    if not isinstance(value, (int, float)) or np.isnan(value):
        raise ValueError(f"{parameter_name} must be a valid number")
    
    if min_val is not None and value < min_val:
        raise ValueError(f"{parameter_name} must be >= {min_val}, got {value}")
    
    if max_val is not None and value > max_val:
        raise ValueError(f"{parameter_name} must be <= {max_val}, got {value}")


def create_research_summary_dict(results: Dict[str, Any], 
                                paper_context: str = "") -> Dict[str, Any]:
    """
    Create a research-oriented summary dictionary.
    
    Args:
        results: Analysis results
        paper_context: Context from research paper
        
    Returns:
        Research-friendly summary dictionary
    """
    summary = {
        'analysis_timestamp': generate_analysis_timestamp(),
        'paper_context': paper_context,
        'key_findings': {},
        'statistical_significance': {},
        'research_implications': {},
        'technical_details': results
    }
    
    # Extract key findings if available
    if 'iri_vs_campaigns_ratio' in results:
        summary['key_findings']['effectiveness_ratio'] = results['iri_vs_campaigns_ratio']
        summary['key_findings']['interpretation'] = (
            f"2019 IRI was {results['iri_vs_campaigns_ratio']:.1f}x more effective per dose"
        )
    
    if 'statistical_significance' in results:
        summary['statistical_significance']['p_value'] = results['statistical_significance']
        summary['statistical_significance']['significant'] = results['statistical_significance'] < 0.05
    
    return summary
