"""
Configuration Management

This module provides configuration management for measles transmission
modeling parameters, file paths, and analysis settings.
"""

import os
import yaml
import json
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict
import warnings


@dataclass
class ModelParameters:
    """
    Configuration for transmission model parameters.
    
    These parameters correspond to the mathematical model described
    in the research paper.
    """
    # Transmission model parameters (Equation 3)
    beta_correlation: float = 3.0      # Seasonal correlation parameter
    tau: int = 26                      # Biweekly time steps per year
    alpha: float = 1.0                 # Infection scaling parameter
    
    # SIA effectiveness parameters
    mu_guess: float = 0.1              # Initial SIA effectiveness estimate
    mu_bounds: tuple = (0.0, 1.0)      # SIA effectiveness bounds
    
    # Vaccine efficacy parameters
    mcv1_efficacy: float = 0.825       # MCV1 vaccine efficacy
    mcv2_efficacy: float = 0.95        # MCV2 vaccine efficacy
    sia_efficacy: float = 0.8          # SIA vaccine efficacy
    
    # Reporting rate parameters
    min_reporting_rate: float = 0.0005 # Minimum reporting rate
    max_reporting_rate: float = 1.0    # Maximum reporting rate
    smoothing_strength: float = 3.0    # Temporal smoothing strength
    
    # Optimization parameters
    convergence_tolerance: float = 1e-6
    max_iterations: int = 100
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModelParameters':
        """Create from dictionary."""
        return cls(**data)


@dataclass
class DataPaths:
    """Configuration for data file paths."""
    data_directory: str = "_data/"
    surveillance_file: str = "southern_states_epi_timeseries.csv"
    coverage_file: str = "survey_mcv1_summary_stats.csv"
    sia_calendar_file: str = "imputed_sia_calendar_by_state.csv"
    birth_data_file: str = "monthly_births_by_state.csv"
    state_mapping_file: str = "states_and_regions.csv"
    
    # Output paths
    output_directory: str = "_plots/"
    pickle_directory: str = "pickle_jar/"
    
    def get_full_path(self, filename: str) -> str:
        """Get full path for a data file."""
        return os.path.join(self.data_directory, filename)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class AnalysisSettings:
    """Configuration for analysis settings."""
    # Default state for analysis
    default_state: str = "lagos"
    
    # Confidence levels
    confidence_level: float = 0.95
    
    # Forecasting settings
    default_forecast_horizon: int = 36  # months
    forecast_samples: int = 1000
    
    # Validation settings
    min_time_coverage_years: float = 1.0
    strict_validation: bool = False
    
    # Plotting settings
    figure_dpi: int = 300
    figure_format: str = "png"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


class ConfigManager:
    """
    Manages configuration for measles transmission modeling.
    
    This class provides centralized configuration management for model
    parameters, file paths, and analysis settings. It supports loading
    configuration from files and provides sensible defaults based on
    the research paper.
    
    Example:
        >>> config = ConfigManager()
        >>> config.load_config("config/model_config.yaml")
        >>> params = config.get_model_parameters()
        >>> print(f"Beta correlation: {params.beta_correlation}")
        
        >>> # Update specific parameters
        >>> config.update_model_parameter("tau", 24)
        >>> config.save_config("config/updated_config.yaml")
    
    Configuration File Format:
        The configuration can be saved/loaded as YAML or JSON:
        
        ```yaml
        model_parameters:
          beta_correlation: 3.0
          tau: 26
          mu_guess: 0.1
        
        data_paths:
          data_directory: "_data/"
          surveillance_file: "southern_states_epi_timeseries.csv"
        
        analysis_settings:
          default_state: "lagos"
          confidence_level: 0.95
        ```
    """
    
    def __init__(self, config_file: Optional[str] = None):
        """
        Initialize configuration manager.
        
        Args:
            config_file: Optional path to configuration file
        """
        # Initialize with defaults
        self.model_parameters = ModelParameters()
        self.data_paths = DataPaths()
        self.analysis_settings = AnalysisSettings()
        
        # Load configuration file if provided
        if config_file and os.path.exists(config_file):
            self.load_config(config_file)
    
    def load_config(self, config_file: str) -> None:
        """
        Load configuration from file.
        
        Args:
            config_file: Path to YAML or JSON configuration file
            
        Raises:
            FileNotFoundError: If config file doesn't exist
            ValueError: If config file format is invalid
        """
        if not os.path.exists(config_file):
            raise FileNotFoundError(f"Configuration file not found: {config_file}")
        
        try:
            with open(config_file, 'r') as f:
                if config_file.endswith('.yaml') or config_file.endswith('.yml'):
                    config_data = yaml.safe_load(f)
                elif config_file.endswith('.json'):
                    config_data = json.load(f)
                else:
                    raise ValueError("Configuration file must be YAML or JSON")
            
            # Update configuration sections
            if 'model_parameters' in config_data:
                self.model_parameters = ModelParameters.from_dict(config_data['model_parameters'])
            
            if 'data_paths' in config_data:
                # Update data paths while preserving defaults
                for key, value in config_data['data_paths'].items():
                    if hasattr(self.data_paths, key):
                        setattr(self.data_paths, key, value)
            
            if 'analysis_settings' in config_data:
                # Update analysis settings while preserving defaults
                for key, value in config_data['analysis_settings'].items():
                    if hasattr(self.analysis_settings, key):
                        setattr(self.analysis_settings, key, value)
            
            print(f"✓ Configuration loaded from {config_file}")
            
        except Exception as e:
            raise ValueError(f"Failed to load configuration: {str(e)}")
    
    def save_config(self, config_file: str, format: str = "yaml") -> None:
        """
        Save current configuration to file.
        
        Args:
            config_file: Path to save configuration
            format: File format ("yaml" or "json")
        """
        config_data = {
            'model_parameters': self.model_parameters.to_dict(),
            'data_paths': self.data_paths.to_dict(),
            'analysis_settings': self.analysis_settings.to_dict()
        }
        
        try:
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            
            with open(config_file, 'w') as f:
                if format.lower() == "yaml":
                    yaml.dump(config_data, f, default_flow_style=False, indent=2)
                elif format.lower() == "json":
                    json.dump(config_data, f, indent=2)
                else:
                    raise ValueError("Format must be 'yaml' or 'json'")
            
            print(f"✓ Configuration saved to {config_file}")
            
        except Exception as e:
            raise RuntimeError(f"Failed to save configuration: {str(e)}")
    
    def get_model_parameters(self) -> ModelParameters:
        """Get model parameters configuration."""
        return self.model_parameters
    
    def get_data_paths(self) -> DataPaths:
        """Get data paths configuration."""
        return self.data_paths
    
    def get_analysis_settings(self) -> AnalysisSettings:
        """Get analysis settings configuration."""
        return self.analysis_settings
    
    def update_model_parameter(self, parameter: str, value: Any) -> None:
        """
        Update a specific model parameter.
        
        Args:
            parameter: Parameter name
            value: New parameter value
            
        Raises:
            AttributeError: If parameter doesn't exist
        """
        if not hasattr(self.model_parameters, parameter):
            raise AttributeError(f"Unknown model parameter: {parameter}")
        
        setattr(self.model_parameters, parameter, value)
        print(f"✓ Updated {parameter} = {value}")
    
    def update_data_path(self, path_name: str, path_value: str) -> None:
        """
        Update a specific data path.
        
        Args:
            path_name: Path configuration name
            path_value: New path value
        """
        if not hasattr(self.data_paths, path_name):
            raise AttributeError(f"Unknown data path: {path_name}")
        
        setattr(self.data_paths, path_name, path_value)
        print(f"✓ Updated {path_name} = {path_value}")
    
    def update_analysis_setting(self, setting: str, value: Any) -> None:
        """
        Update a specific analysis setting.
        
        Args:
            setting: Setting name
            value: New setting value
        """
        if not hasattr(self.analysis_settings, setting):
            raise AttributeError(f"Unknown analysis setting: {setting}")
        
        setattr(self.analysis_settings, setting, value)
        print(f"✓ Updated {setting} = {value}")
    
    def validate_configuration(self) -> Dict[str, Any]:
        """
        Validate current configuration for consistency and completeness.
        
        Returns:
            Dictionary with validation results
        """
        validation_results = {
            'status': 'valid',
            'issues': [],
            'warnings': []
        }
        
        # Validate model parameters
        params = self.model_parameters
        
        if params.beta_correlation <= 0:
            validation_results['issues'].append("Beta correlation must be positive")
        
        if params.tau <= 0:
            validation_results['issues'].append("Tau (time periods) must be positive")
        
        if not (0 <= params.mu_guess <= 1):
            validation_results['issues'].append("Initial mu guess must be between 0 and 1")
        
        # Validate data paths
        data_dir = self.data_paths.data_directory
        if not os.path.exists(data_dir):
            validation_results['warnings'].append(f"Data directory does not exist: {data_dir}")
        
        # Validate analysis settings
        if not (0 < self.analysis_settings.confidence_level < 1):
            validation_results['issues'].append("Confidence level must be between 0 and 1")
        
        # Determine overall status
        if validation_results['issues']:
            validation_results['status'] = 'invalid'
        elif validation_results['warnings']:
            validation_results['status'] = 'warning'
        
        return validation_results
    
    def get_state_specific_config(self, state: str) -> Dict[str, Any]:
        """
        Get configuration customized for a specific state.
        
        Args:
            state: State name
            
        Returns:
            Dictionary with state-specific configuration
        """
        # Create a copy of current configuration
        state_config = {
            'state': state.lower(),
            'model_parameters': self.model_parameters.to_dict(),
            'data_paths': self.data_paths.to_dict(),
            'analysis_settings': self.analysis_settings.to_dict()
        }
        
        # Update default state
        state_config['analysis_settings']['default_state'] = state.lower()
        
        return state_config
    
    def create_default_config_file(self, config_file: str) -> None:
        """
        Create a default configuration file with current settings.
        
        Args:
            config_file: Path for new configuration file
        """
        self.save_config(config_file, format="yaml")
        
        # Add comments to the file
        with open(config_file, 'r') as f:
            content = f.read()
        
        header = """# Measles Transmission Model Configuration
# 
# This configuration file contains parameters for the measles transmission
# model as described in the research paper:
# "Routine immunization intensification, vaccination campaigns, and measles
# transmission in Southern Nigeria" (2025)
#
# Model parameters correspond to equations in the paper:
# - beta_correlation: Seasonal correlation parameter (Equation 3)
# - tau: Time periods per year (26 = biweekly)
# - mu_guess: Initial SIA effectiveness estimate
#

"""
        
        with open(config_file, 'w') as f:
            f.write(header + content)
        
        print(f"✓ Default configuration file created: {config_file}")
    
    def __str__(self) -> str:
        """String representation of configuration."""
        return f"""
Configuration Summary
{'='*25}

Model Parameters:
  Beta correlation: {self.model_parameters.beta_correlation}
  Time periods/year: {self.model_parameters.tau}
  Initial mu guess: {self.model_parameters.mu_guess}

Data Paths:
  Data directory: {self.data_paths.data_directory}
  Output directory: {self.data_paths.output_directory}

Analysis Settings:
  Default state: {self.analysis_settings.default_state}
  Confidence level: {self.analysis_settings.confidence_level}
"""
