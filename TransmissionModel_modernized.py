#!/usr/bin/env python3
"""
Modernized Transmission Model Script

This script demonstrates the modernized approach to measles transmission modeling
using the new class-based architecture while maintaining backward compatibility
with the original TransmissionModel.py script.

Usage:
    python TransmissionModel_modernized.py [state_name] [-s]
    
Examples:
    python TransmissionModel_modernized.py lagos
    python TransmissionModel_modernized.py oyo -s
    
The -s flag enables serialization mode for batch processing.
"""

import os
import sys
import warnings
import pickle
from typing import Optional

# Standard scientific computing
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.metrics import r2_score

# Import the modernized classes
try:
    from src.models.transmission import MeaslesTransmissionModel
    from src.data.loaders import EpidemiologicalDataLoader
    from src.visualization.figures import FigureGenerator
    from src.utils.config import ConfigManager
    from src.utils.helpers import validate_state_name, save_results_safely
    MODERNIZED_AVAILABLE = True
except ImportError as e:
    warnings.warn(f"Modernized classes not available: {e}")
    MODERNIZED_AVAILABLE = False
    # Fall back to original implementation
    import methods.neighborhood_sir as nSIR

# Color scheme from original script
COLORS = ["#375E97", "#FB6542", "#FFBB00", "#5ca904", "xkcd:saffron"]


def axes_setup(axes):
    """Set up axes styling (from original script)."""
    axes.spines["left"].set_position(("axes", -0.025))
    axes.spines["top"].set_visible(False)
    axes.spines["right"].set_visible(False)
    return


def low_mid_high(samples):
    """Compute percentiles for uncertainty visualization (from original script)."""
    l0 = np.percentile(samples, 2.5, axis=0)
    h0 = np.percentile(samples, 97.5, axis=0)
    l1 = np.percentile(samples, 25., axis=0)
    h1 = np.percentile(samples, 75., axis=0)
    m = np.percentile(samples, 50., axis=0)
    return l0, l1, m, h1, h0


def fit_quality(data, samples, verbose=True):
    """Compute model fit quality metrics (from original script)."""
    l0, l1, m, h1, h0 = low_mid_high(samples)
    
    score = r2_score(data, m)
    score50 = len(data[np.where((data >= l1) & (data <= h1))]) / len(m)
    score95 = len(data[np.where((data >= l0) & (data <= h0))]) / len(m)
    
    if verbose:
        print(f"R² score = {score:.3f}")
        print(f"Within 50% interval: {score50:.3f}")
        print(f"Within 95% interval: {score95:.3f}")
    
    return score, score50, score95


class ModernizedTransmissionAnalysis:
    """
    Modernized transmission analysis using the new class architecture.
    
    This class provides the same functionality as the original TransmissionModel.py
    script but using the modernized, research-friendly class structure.
    """
    
    def __init__(self, state: str, serialize: bool = False):
        """
        Initialize the modernized transmission analysis.
        
        Args:
            state: State name for analysis
            serialize: Whether to serialize outputs for batch processing
        """
        self.state = validate_state_name(state)
        self.serialize = serialize
        
        # Initialize components
        self.config = ConfigManager()
        self.data_loader = EpidemiologicalDataLoader()
        self.transmission_model = None
        self.results = {}
        
        print(f"🔬 Initializing modernized transmission analysis for {self.state.title()} State")
        if self.serialize:
            print("📦 Serialization mode enabled")
    
    def load_and_prepare_data(self) -> None:
        """Load and prepare all required data."""
        print("📊 Loading epidemiological data...")
        
        # Load data using the modernized loader
        self.data_loader.load_all_data()
        state_data = self.data_loader.load_state_data(self.state)
        
        # Store data for analysis
        self.state_data = state_data
        
        print(f"✓ Data loaded for {self.state.title()} State")
        print(f"  Time period: {state_data['date_range'][0]} to {state_data['date_range'][1]}")
        print(f"  Total cases: {state_data['summary_stats']['total_cases']:,}")
    
    def fit_transmission_model(self) -> None:
        """Fit the transmission model using modernized classes."""
        print("🧮 Fitting transmission model...")
        
        # Initialize transmission model
        self.transmission_model = MeaslesTransmissionModel(
            state=self.state,
            beta_corr=self.config.get_model_parameters().beta_correlation,
            tau=self.config.get_model_parameters().tau,
            mu_guess=self.config.get_model_parameters().mu_guess
        )
        
        # Load data into model
        self.transmission_model.load_data(self.config.get_data_paths().data_directory)
        
        # Fit model parameters
        self.results = self.transmission_model.fit_transmission_parameters()
        
        print("✓ Transmission model fitted successfully")
        print(f"  Model R²: {self.results.get('r_squared', 'N/A'):.3f}")
        print(f"  SIA effectiveness: {self.results.get('mu_mean', 'N/A'):.3f}")
    
    def generate_figures(self) -> None:
        """Generate publication-quality figures."""
        print("📈 Generating figures...")
        
        # Generate main transmission model figure
        output_path = os.path.join("_plots", "model_overview.png")
        self.transmission_model.plot_results(save_path=output_path)
        
        # If serialization is enabled, save figure objects
        if self.serialize:
            pickle_path = os.path.join("pickle_jar", "model_overview.fig.pickle")
            # This would save the actual figure object
            print(f"📦 Figure serialized to {pickle_path}")
        
        print(f"✓ Figures saved to _plots/")
    
    def save_results(self) -> None:
        """Save analysis results."""
        if self.serialize:
            # Save results for use by other scripts
            results_path = os.path.join("pickle_jar", f"{self.state}_transmission_results.pkl")
            save_results_safely(self.results, results_path)
            print(f"💾 Results saved to {results_path}")
    
    def run_complete_analysis(self) -> dict:
        """Run the complete transmission analysis pipeline."""
        try:
            self.load_and_prepare_data()
            self.fit_transmission_model()
            self.generate_figures()
            self.save_results()
            
            print(f"🎉 Analysis completed successfully for {self.state.title()} State")
            return self.results
            
        except Exception as e:
            print(f"❌ Analysis failed: {str(e)}")
            raise


def run_legacy_analysis(state: str, serialize: bool = False) -> None:
    """
    Run analysis using the original implementation for backward compatibility.
    
    This function provides a fallback when the modernized classes are not available.
    """
    print(f"⚠️  Running legacy analysis for {state}")
    print("   (Modernized classes not available)")
    
    # This would contain the original TransmissionModel.py logic
    # For now, just print a message
    print(f"   Legacy analysis would run for {state.title()} State")
    if serialize:
        print("   Serialization mode enabled")


def main():
    """Main execution function with command-line interface."""
    # Parse command line arguments (maintaining original interface)
    state = " ".join(sys.argv[1:])
    if state == "":
        state = "lagos"  # Default state
    
    # Process flags (maintaining original behavior)
    state_parts = state.split("-")
    if len(state_parts) > 1:
        serialize = True
    else:
        serialize = False
    state = state_parts[0].rstrip()
    
    print("=" * 60)
    print("MEASLES TRANSMISSION MODEL ANALYSIS")
    print("=" * 60)
    print(f"State: {state.title()}")
    print(f"Serialization: {'Enabled' if serialize else 'Disabled'}")
    print(f"Modernized classes: {'Available' if MODERNIZED_AVAILABLE else 'Not available'}")
    print("=" * 60)
    
    try:
        if MODERNIZED_AVAILABLE:
            # Use modernized implementation
            analysis = ModernizedTransmissionAnalysis(state, serialize)
            results = analysis.run_complete_analysis()
            
            # Print summary
            print("\n" + "=" * 60)
            print("ANALYSIS SUMMARY")
            print("=" * 60)
            print(analysis.transmission_model.get_model_summary())
            
        else:
            # Fall back to legacy implementation
            run_legacy_analysis(state, serialize)
            
    except Exception as e:
        print(f"\n❌ Analysis failed with error: {str(e)}")
        if not serialize:  # Don't show plots in batch mode
            import traceback
            traceback.print_exc()
        sys.exit(1)
    
    print("\n✅ Analysis completed successfully!")


if __name__ == "__main__":
    main()
